/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tsingyanprod.service.codegpt.handler.completion;

import com.alipay.tsingyanprod.model.model.TaskContextModel;
import com.alipay.tsingyanprod.model.request.CompletionAlgRequest;
import com.alipay.tsingyanprod.model.response.CompletionAlgResponse;
import com.alipay.tsingyanprod.service.codegpt.external.alg.service.CompletionService;

/**
 * <AUTHOR>
 * @version CompletionHandler.java, v 0.1 2023年04月27日 15:05 xiaobin
 */
public class CompletionHandler extends AbstractCompletionAlgHandler<CompletionAlgRequest, CompletionAlgResponse> {

    /**
     * 抽象handler构造函数
     *
     * @param server 算法服务
     */
    public CompletionHandler(CompletionService server) {
        super(server);
    }

    @Override
    public CompletionAlgResponse codeCompletion(TaskContextModel<CompletionAlgRequest, CompletionAlgResponse> taskContextModel) {
        CompletionAlgRequest request = taskContextModel.getRequest();
        MODEL_LOGGER.info("开始执行代码补全处理，traceId: {}, userId: {}, language: {}",
                request.getTraceId(), request.getUserId(), request.getLanguage());

        CompletionAlgResponse result = completionAlgService.completion(request, taskContextModel.getRecord());
        taskContextModel.setResult(result);

        MODEL_LOGGER.info("代码补全处理完成，traceId: {}, 生成结果数量: {}",
                request.getTraceId(), result != null && result.getGeneratedTextArr() != null ? result.getGeneratedTextArr().size() : 0);
        return null;
    }
}