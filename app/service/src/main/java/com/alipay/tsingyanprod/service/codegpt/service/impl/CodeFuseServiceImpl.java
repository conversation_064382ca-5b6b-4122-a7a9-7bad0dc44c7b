/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tsingyanprod.service.codegpt.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alipay.antq.common.utils.StandardCharsets;
import com.alipay.tsingyanprod.dal.example.ChatMessageDOExample;
import com.alipay.tsingyanprod.dal.mapper.ChatMessageDOMapper;
import com.alipay.tsingyanprod.dal.mapper.PluginFileDOMapper;
import com.alipay.tsingyanprod.dal.mapper.UserDOMapper;
import com.alipay.tsingyanprod.model.AppConstants;
import com.alipay.tsingyanprod.model.constant.AbTestConstants;
import com.alipay.tsingyanprod.model.constant.PromptConstants;
import com.alipay.tsingyanprod.model.domain.ChatMessageDO;
import com.alipay.tsingyanprod.model.domain.UserDO;
import com.alipay.tsingyanprod.model.enums.*;
import com.alipay.tsingyanprod.model.enums.repo.ApplyFromEnum;
import com.alipay.tsingyanprod.model.enums.repo.KnowledgeTypeEnum;
import com.alipay.tsingyanprod.model.exception.BizException;
import com.alipay.tsingyanprod.model.exception.InvalidReqException;
import com.alipay.tsingyanprod.model.model.*;
import com.alipay.tsingyanprod.model.request.*;
import com.alipay.tsingyanprod.model.request.bo.AgentRequestBO;
import com.alipay.tsingyanprod.model.request.codeedit.CodeEditRequestBean;
import com.alipay.tsingyanprod.model.request.completion.AlgRequest;
import com.alipay.tsingyanprod.model.request.localcore.DeepSearchCodeBaseLocalDTO;
import com.alipay.tsingyanprod.model.request.localcore.DeepSearchExplainCodeDTO;
import com.alipay.tsingyanprod.model.request.localcore.LocalRepoRequestDTO;
import com.alipay.tsingyanprod.model.request.localcore.bo.ChatExtraInfoBO;
import com.alipay.tsingyanprod.model.request.localcore.bo.LocalCoreRagBO;
import com.alipay.tsingyanprod.model.request.openapi.OpenApiCompletionRequestBean;
import com.alipay.tsingyanprod.model.request.repo.BaseRepoRequestBean;
import com.alipay.tsingyanprod.model.request.repo.CodeReferenceRequestBean;
import com.alipay.tsingyanprod.model.response.*;
import com.alipay.tsingyanprod.model.response.codeedit.CodeEditsConfigBO;
import com.alipay.tsingyanprod.model.response.repo.RepoResponse;
import com.alipay.tsingyanprod.service.ABTestService;
import com.alipay.tsingyanprod.service.FunctionOnStreamService;
import com.alipay.tsingyanprod.service.UserAuthTokenService;
import com.alipay.tsingyanprod.service.UserService;
import com.alipay.tsingyanprod.service.codegpt.external.alg.service.*;
import com.alipay.tsingyanprod.service.codegpt.framework.TaskExecutor;
import com.alipay.tsingyanprod.service.codegpt.framework.TaskFactory;
import com.alipay.tsingyanprod.service.codegpt.framework.TaskWorkFlow;
import com.alipay.tsingyanprod.service.codegpt.framework.TaskWorkFlowBuilder;
import com.alipay.tsingyanprod.service.codegpt.handler.AbstractCodeChatAfterHandler;
import com.alipay.tsingyanprod.service.codegpt.handler.chat.ChatGptModelHandler;
import com.alipay.tsingyanprod.service.codegpt.handler.completion.CodeEditHandler;
import com.alipay.tsingyanprod.service.codegpt.handler.completion.CompletionHandler;
import com.alipay.tsingyanprod.service.codegpt.handler.completion.OpenApiCompletionHandler;
import com.alipay.tsingyanprod.service.codegpt.handler.completion.datahandler.ChatTaskDataHandler;
import com.alipay.tsingyanprod.service.codegpt.handler.completion.datahandler.CodeCompletionTaskDataHandler;
import com.alipay.tsingyanprod.service.codegpt.handler.completion.datahandler.UnitTestDataHandler;
import com.alipay.tsingyanprod.service.codegpt.service.CacheService;
import com.alipay.tsingyanprod.service.codegpt.service.ChatMessageService;
import com.alipay.tsingyanprod.service.codegpt.service.CodeFuseService;
import com.alipay.tsingyanprod.service.codegpt.service.QueryConfigService;
import com.alipay.tsingyanprod.service.config.ServiceConstant;
import com.alipay.tsingyanprod.service.drm.BizSwitch;
import com.alipay.tsingyanprod.service.drm.CodeGPTDrmConfig;
import com.alipay.tsingyanprod.service.localcore.LocalCoreService;
import com.alipay.tsingyanprod.service.mayastream.MayaStreamIntentionService;
import com.alipay.tsingyanprod.service.mayastream.factory.MayaStreamIntentionFactory;
import com.alipay.tsingyanprod.utils.CommonUtils;
import com.alipay.tsingyanprod.utils.EventLogUtils;
import com.alipay.tsingyanprod.utils.InterfaceRecordModelUtils;
import com.alipay.tsingyanprod.utils.PromptDealUtils;
import com.alipay.tsingyanprod.utils.code.CodeUtils;
import com.alipay.tsingyanprod.utils.http.HttpUtil;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version CodeFuseServiceImpl.java, v 0.1 2023年04月24日 17:21 xiaobin
 */
@Service
public class CodeFuseServiceImpl implements CodeFuseService {

    private static final Logger AI_PARTNER_LOGGER = LoggerFactory.getLogger("AI-PARTNER");

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    private static final Logger LOGGER = LoggerFactory.getLogger(CodeFuseServiceImpl.class);

    private static final String[] CODE_GENERATE_TEST_TYPE_LIST = {"junit4", "junit5", "使用junit4为以下代码写单测",
            "使用junit5为以下代码写单测"};

    private static final String[] EXPLAIN_MAP_LIST = {"更简单的", "更详细"};

    private static final String[] EXPLAIN_LANGUAGE_TYPE_LIST = {"解释代码", "解释下代码意图", "更详细的解释", "更简单的解释"};

    private static final Set<String> SELECTED_KEYWORDS = new HashSet<>();

    static {
        // 中文关键词
        SELECTED_KEYWORDS.addAll(Arrays.asList("选择", "选中", "选定", "当前", "打开"));
        // 英文关键词
        SELECTED_KEYWORDS.addAll(
                Arrays.asList("open", "current", "present", "selected", "select", "choose", "chosen", "designated", "pick"));
    }

    private static final String[] ERROR_MESSAGE_PROMPT = {"分析以下命令以及错误信息给出详细的修改示例，并用中文解释",
            "分析以下异常信息以及代码给出详细的修改示例，并用中文解释"};

    private static final String LAST_QUESTION_IS_OPTIMIZE_CODE_MARK = "以下是我需要优化的代码，请按照上述要求给我优化";

    /**
     * 图生代码功能权限分流key
     */
    private static final String PICTURE_TO_CODE_PERMISSIONS = "PICTURE_TO_CODE_PERMISSIONS";

    /**
     * agent shell命令查询功能权限分流key
     */
    private static final String AGENT_TERMINAL_PERMISSIONS = "AGENT_TERMINAL_PERMISSIONS";

    /**
     * action2Code对话功能权限分流key
     */
    private static final String ACTION_TO_CODE_PERMISSIONS = "ACTION_TO_CODE_PERMISSIONS";

    /**
     * agent开放仓库问答功能权限分流key
     */
    private static final String KNOWLEDGE_CODEBASE_PERMISSIONS = "KNOWLEDGE_CODEBASE_PERMISSIONS";

    /**
     * 本地对话是否走本地核心分流
     */
    private static final String LOCAL_AGENT_CHAT_RAG_PERMISSIONS = "LOCAL_AGENT_CHAT_RAG_PERMISSIONS";

    /**
     * 工作流分流key
     */
    private static final String WORK_FLOW_API_PERMISSIONS = "WORK_FLOW_API_PERMISSIONS";

    /**
     * CODEBASE仓库问答权限
     */
    private static final String DEEP_SEARCH_CODE_BASE_PERMISSION = "DEEP_SEARCH_CODE_BASE_PERMISSION";

    /**
     * EXPLAIN_CODE解释代码权限
     */
    private static final String DEEP_SEARCH_EXPLAIN_CODE_PERMISSION = "DEEP_SEARCH_EXPLAIN_CODE_PERMISSION";

    /**
     * AI Partner Agent权限
     */
    private static final String DEEP_SEARCH_AI_PARTNER_AGENT_PERMISSION = "DEEP_SEARCH_AI_PARTNER_AGENT_PERMISSION";

    /**
     * ai composer功能分流
     */
    private static final String AI_PARTNER_PERMISSIONS = "AI_PARTNER_PERMISSIONS";

    private static final String AGENT_PREFIX = "agent/";

    private static final String KNOWLEDGE_PREFIX = "knowledge/";

    public static final String QUERY_CONFIG_DATA_CACHE_PREFIX = "QUERY_CONFIG_DATA:";

    /**
     * 代码补全执行器
     */
    private TaskExecutor codeCompletionExecutor;

    /**
     * codeEdit执行器
     */
    private TaskExecutor codeEditExecutor;

    /**
     * nextTab执行器
     */
    private TaskExecutor nextTabExecutor;

    /**
     * 代码补全执行器(OpenApi)
     */
    private TaskExecutor codeCompletionOpenApiExecutor;

    /**
     * 纯代码对话执行器
     */
    private TaskExecutor codeChatExecutor;

    /**
     * 本地仓库问答执行器非流式
     */
    private TaskExecutor localRepoCodeChatExecutor;

    @Resource
    private ChatMessageService chatMessageService;

    /**
     * 代码补全流式执行器
     */
    private TaskExecutor chatStreamExecutor;

    /**
     * maya流式执行器
     */
    private TaskExecutor mayaStreamExecutor;

    /**
     * 测试代码相关执行器
     */
    private TaskExecutor testCaseExecutor;

    @Resource
    private LocalCoreService localCoreService;

    @Resource
    private CodeGPTDrmConfig drmConfig;

    @Resource
    private CodeCompletionTaskDataHandler codeCompletionTaskDataHandler;

    @Resource
    private ChatTaskDataHandler chatTaskDataHandler;

    @Resource
    private ChatMessageDOMapper chatMessageDOMapper;

    @Resource
    private UserService userService;

    @Resource
    private ABTestService abTestService;

    @Resource
    private UserDOMapper userDOMapper;

    @Resource
    private UserAuthTokenService userTokenService;

    /**
     * 随机对象,在ab实验时用来随机分流的
     */
    private final Random random = new Random();

    @Resource
    private PluginFileDOMapper pluginFileDOMapper;

    @Resource
    private ChatService chatService;

    @Resource
    private CompletionService completionService;

    @Resource
    private BizSwitch bizSwitch;

    @Resource
    private TokenizedService tokenizedService;

    @Resource
    private MayaStreamIntentionFactory mayaStreamIntentionFactory;

    @Resource
    private CodeEditService codeEditService;

    @Resource
    private NextTabService nextTabService;

    @Resource
    private QueryConfigService queryConfigService;

    @Resource
    private CacheService cacheService;

    @Resource
    private FunctionOnStreamService functionOnStreamService;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        CompletionHandler completionHandler = new CompletionHandler(completionService);
        CodeEditHandler codeEditHandler = new CodeEditHandler(codeEditService, nextTabService);
        TaskWorkFlow codeCompletion = TaskWorkFlowBuilder.buildTaskWork().addHandlerFun(
                        codeCompletionTaskDataHandler::lowIncomeBeforeFilter).addHandlerFun(completionHandler::codeCompletion).addHandlerFun(
                        codeCompletionTaskDataHandler::lowIncomeAfterFilter).addHandlerFun(codeCompletionTaskDataHandler::bracketMatchingHandle)
                .addHandlerFun(codeCompletionTaskDataHandler::codeCommentHandler).build();
        codeCompletionExecutor = TaskFactory.builder().taskWorkFlow(codeCompletion).build();

        TaskWorkFlow codeEdit = TaskWorkFlowBuilder.buildTaskWork().addHandlerFun(codeEditHandler::lowIncomeBeforeFilter).addHandlerFun(
                codeEditHandler::codeEdit).addHandlerFun(codeEditHandler::lowIncomeAfterFilter).build();
        codeEditExecutor = TaskFactory.builder().taskWorkFlow(codeEdit).build();

        TaskWorkFlow nextTab = TaskWorkFlowBuilder.buildTaskWork().addHandlerFun(codeEditHandler::nextTab).build();
        nextTabExecutor = TaskFactory.builder().taskWorkFlow(nextTab).build();

        ChatGptModelHandler chatGptModelHandler = new ChatGptModelHandler(chatService);
        TaskWorkFlow codeChat = TaskWorkFlowBuilder.buildTaskWork().addHandlerFun(chatGptModelHandler::codeChat).addMulHandlerFun(t -> {
            ChatRequestServerModel request = (ChatRequestServerModel) t.getRequest();
            return request.getIntention().name();
        }, AbstractCodeChatAfterHandler.getAfterHandlerMap()).build();
        codeChatExecutor = TaskFactory.builder().taskWorkFlow(codeChat).build();

        // maya流式执行器
        TaskWorkFlow mayaStreamFlow = TaskWorkFlowBuilder.buildTaskWork().addHandlerFun(chatGptModelHandler::mayaRequestByStream).build();
        mayaStreamExecutor = TaskFactory.builder().taskWorkFlow(mayaStreamFlow).build();

        // 本地仓库问答独立出来
        TaskWorkFlow localRepoCodeChat = TaskWorkFlowBuilder.buildTaskWork().addHandlerFun(chatGptModelHandler::codeChat).addMulHandlerFun(
                t -> {
                    ChatRequestServerModel request = (ChatRequestServerModel) t.getRequest();
                    return request.getIntention().name();
                }, AbstractCodeChatAfterHandler.getAfterHandlerMap()).build();
        localRepoCodeChatExecutor = TaskFactory.builder().taskWorkFlow(localRepoCodeChat).build();

        TaskWorkFlow streamChat = TaskWorkFlowBuilder.buildTaskWork().addHandlerFun(chatTaskDataHandler::changePromptWithGenTestCase)
                .addHandlerFun(chatGptModelHandler::chatOnStream).build();
        chatStreamExecutor = TaskFactory.builder().taskWorkFlow(streamChat).build();

        TaskWorkFlow codeCompletionByOpenApi = TaskWorkFlowBuilder.buildTaskWork().addHandlerFun(
                new OpenApiCompletionHandler(completionService)::codeCompletion).build();
        codeCompletionOpenApiExecutor = TaskFactory.builder().taskWorkFlow(codeCompletionByOpenApi).build();

        UnitTestDataHandler unitTestDataHandler = new UnitTestDataHandler();
        TaskWorkFlow mergeTask = TaskWorkFlowBuilder.buildTaskWork().addHandlerFun(unitTestDataHandler::gptDataPreProcess).addHandlerFun(
                unitTestDataHandler::mergeTestCase).addHandlerFun(unitTestDataHandler::gptDataPostProcess).build();
        testCaseExecutor = TaskFactory.builder().taskWorkFlow(mergeTask).build();
    }

    /**
     * 代码补全,使用{@link CodeFuseServiceImpl#codeCompletionExecutor} 执行器
     *
     * @param request
     * @param recordModel
     * @return
     */
    @Override
    public CompletionAlgResponse codeCompletion(CompletionRequestBean request, InterfaceRecordModel recordModel) {
        //构建请求
        CompletionAlgRequest serviceRequest = new CompletionAlgRequest(request);

        LOGGER.info("接收到代码补全请求，sessionId: {}, userId: {}, language: {}, promptLength: {}",
                request.getSessionId(), request.getUserId(), request.getLanguage(),
                request.getPrompt() != null ? request.getPrompt().length() : 0);

        //构建执行器上下文
        TaskContextModel<CompletionAlgRequest, CompletionAlgResponse> taskContextModel = new TaskContextModel<>(serviceRequest);
        taskContextModel.setRecord(recordModel);
        //调用执行器
        codeCompletionExecutor.execute(taskContextModel);
        return taskContextModel.getResult();
    }

    @Override
    public CompletionResultModel nextTab(NextTabRequestBean nextTabRequestBean, InterfaceRecordModel recordModel) {
        //构建执行器上下文
        TaskContextModel<NextTabRequestBean, CompletionResultModel> taskContextModel = new TaskContextModel<>(nextTabRequestBean);
        taskContextModel.setRecord(recordModel);
        //调用执行器
        nextTabExecutor.execute(taskContextModel);
        return taskContextModel.getResult();
    }

    @Override
    public CompletionAlgResponse codeEdit(CodeEditRequestBean request, InterfaceRecordModel recordModel) {
        TaskContextModel<CodeEditRequestBean, CompletionAlgResponse> taskContextModel = new TaskContextModel<>(request);
        taskContextModel.setRecord(recordModel);
        try {
            codeEditExecutor.execute(taskContextModel);
        } catch (Exception e) {
            LOGGER.error("Error executing codeEditExecutor: {}", e.getMessage(), e);
        }
        return taskContextModel.getResult();
    }

    @Override
    public CompletionAlgResponse codeCompletionByOpenApi(OpenApiCompletionRequestBean request, InterfaceRecordModel recordModel) {
        TaskContextModel<OpenApiCompletionRequestBean, CompletionAlgResponse> taskContextModel = new TaskContextModel<>(request);
        taskContextModel.setRecord(recordModel);
        codeCompletionOpenApiExecutor.execute(taskContextModel);
        return taskContextModel.getResult();
    }

    @Override
    public CodeChatResponseBean codeChat(CodeChatRequestBean request, InterfaceRecordModel recordModel) {
        IntentionEnum intention = EnumUtil.fromString(IntentionEnum.class, request.getIntention(), IntentionEnum.UNKNOWN);
        List<ChatMessageModel> chatMessageList = new ArrayList<>();
        chatMessageList.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), request.getQuestion()));
        AlgRequest<List<ChatMessageModel>> gptAlgRequest = new AlgRequest<>(chatMessageList, drmConfig.getCodeGptModelConfig());
        String sessionId = UUID.randomUUID().toString();
        //问答日志埋点
        EventLogUtils.recordLogForComment(request,
                EnumUtil.fromString(ProductTypeEnum.class, request.getProductType(), ProductTypeEnum.UNKNOWN), sessionId);
        ChatRequestServerModel serviceRequest = new ChatRequestServerModel(sessionId, request.getUserId(), gptAlgRequest);
        serviceRequest.setOptionLanguage(request.getOptionLanguage());
        serviceRequest.setIntention(intention);
        serviceRequest.setLanguage(LanguageEnum.getLanguageEnum(request.getLanguage()));
        serviceRequest.setProductType(EnumUtil.fromString(ProductTypeEnum.class, request.getProductType(), ProductTypeEnum.UNKNOWN));
        TaskContextModel<ChatRequestServerModel, String> taskContextModel = new TaskContextModel<>(serviceRequest);
        taskContextModel.setRecord(recordModel);
        codeChatExecutor.execute(taskContextModel);
        String result = taskContextModel.getResult();
        return new CodeChatResponseBean(sessionId, result);
    }

    @Override
    public Flux<? extends Object> chatByStream(ChatRequestBean request, InterfaceRecordModel recordModel) {
        String originalQuery = request.getQuestion();
        // 获取基础校验配置（token和最大最对话轮数）
        CodegenChatRequestModel codegenChatRequestModel = JSONObject.parseObject(drmConfig.getTalkStreamBaseCheckConfig(),
                CodegenChatRequestModel.class);
        // 基础校验 如果长度超过限制，直接抛异常让controller捕获 , 如果是仓库问答并且repo参数为空，那么直接返回
        baseCheck(request, codegenChatRequestModel);
        // 本地仓库问答转换默认模型
        if (KnowledgeTypeEnum.CODEBASE_LOCAL.name().equals(request.getKnowledgeType())) {
            changeDefaultModel(request);
        }
        TaskContextModel<ChatRequestServerModel, Flux<ChatFragmentModel>> taskContextModel = new TaskContextModel<>(
                getChatRequestModel(request, codegenChatRequestModel.getMaxRound() - 1));
        recordModel.setInterfaceInput(JSONObject.toJSONString(request));
        taskContextModel.setRecord(recordModel);

        Flux<ChatFragmentModel> chatFlux;
        LocalCoreRagBO localCoreRagBO = request.getLocalCoreRagBO();
        if (null != localCoreRagBO && CollectionUtils.isNotEmpty(localCoreRagBO.getDeepSearchSubQuestions())) {
            chatFlux = deepSearchLlmChat(request, originalQuery, recordModel, localCoreRagBO, taskContextModel);
        } else {
            chatStreamExecutor.execute(taskContextModel);
            chatFlux = taskContextModel.getResult().share();
        }

        Mono<String> algResult = chatFlux.map(ChatFragmentModel::getContent).reduce(new StringBuilder(), StringBuilder::append).map(
                StringBuilder::toString);
        //问答日志埋点
        EventLogUtils.recordLogForChat(request,
                EnumUtil.fromString(ProductTypeEnum.class, request.getProductType(), ProductTypeEnum.UNKNOWN), request.isRetry());
        //将拼接后的字符串存储到数据库
        algResult.subscribe(result -> {
            chatMessageService.updateAnswer(request.getAnswerUid(), result, JSON.toJSONString(taskContextModel.getRequest().getExtended()));
            recordModel.setAlgEndTime(System.currentTimeMillis());
            recordModel.setAlgOutput(result);
            recordModel.setInterfaceResult(result);
            CHAT_LOGGER.info(Base64Encoder.encode(JSONObject.toJSONString(recordModel)));
            LOGGER.info(JSONObject.toJSONString(recordModel));
        });
        return chatFlux;
    }

    /**
     * 构建DeepSearchCodeBaseLocalDTO
     * @param recordModel
     * @param originalQuery
     * @param localCoreRagBO
     * @param chatRequestServerModel
     * @return
     */
    private DeepSearchCodeBaseLocalDTO buildDeepSearchCodeBaseLocalDTO(InterfaceRecordModel recordModel, String originalQuery,
                                                                       LocalCoreRagBO localCoreRagBO,
                                                                       ChatRequestServerModel chatRequestServerModel) {
        DeepSearchCodeBaseLocalDTO deepSearchCodeBaseLocalDTO = new DeepSearchCodeBaseLocalDTO();
        deepSearchCodeBaseLocalDTO.setQuery(originalQuery);
        deepSearchCodeBaseLocalDTO.setDeepSearchReferenceRagList(localCoreRagBO.getDeepSearchReferenceRagList());
        deepSearchCodeBaseLocalDTO.setDeepSearchSubQuestions(localCoreRagBO.getDeepSearchSubQuestions());
        deepSearchCodeBaseLocalDTO.setExtraInfo(new ChatExtraInfoBO(chatRequestServerModel, recordModel));
        return deepSearchCodeBaseLocalDTO;
    }

    /**
     * 构建DeepSearchExplainCodeDTO
     * @param recordModel
     * @param originalQuery
     * @param localCoreRagBO
     * @param chatRequestServerModel
     * @return
     */
    private DeepSearchExplainCodeDTO buildDeepSearchExplainCodeDTO(InterfaceRecordModel recordModel, String originalQuery,
                                                                   LocalCoreRagBO localCoreRagBO,
                                                                   ChatRequestServerModel chatRequestServerModel) {
        DeepSearchExplainCodeDTO deepSearchExplainCodeDTO = new DeepSearchExplainCodeDTO();
        deepSearchExplainCodeDTO.setQuery(originalQuery);
        deepSearchExplainCodeDTO.setDeepSearchReferenceRagList(localCoreRagBO.getDeepSearchReferenceRagList());
        deepSearchExplainCodeDTO.setDeepSearchSubQuestions(localCoreRagBO.getDeepSearchSubQuestions());
        deepSearchExplainCodeDTO.setExtraInfo(new ChatExtraInfoBO(chatRequestServerModel, recordModel));
        return deepSearchExplainCodeDTO;
    }

    /**
     * deepSearchLlmChat
     * @param request
     * @param taskContextModel
     * @return
     */
    private Flux<ChatFragmentModel> deepSearchLlmChat(ChatRequestBean request, String originalQuery, InterfaceRecordModel recordModel,
                                                      LocalCoreRagBO localCoreRagBO,
                                                      TaskContextModel<ChatRequestServerModel, Flux<ChatFragmentModel>> taskContextModel) {
        Flux<ChatFragmentModel> chatFlux;
        if (KnowledgeTypeEnum.CODEBASE_LOCAL.name().equals(request.getKnowledgeType())) {
            DeepSearchCodeBaseLocalDTO deepSearchCodeBaseLocalDTO = buildDeepSearchCodeBaseLocalDTO(recordModel, originalQuery,
                    localCoreRagBO, taskContextModel.getRequest());
            chatFlux = localCoreService.deepSearchCodebaseLocalQa(deepSearchCodeBaseLocalDTO).share();
        } else if (ChatIntentionEnum.EXPLAIN_CODE.name().equals(request.getIntention())) {
            DeepSearchExplainCodeDTO deepSearchExplainCodeDTO = buildDeepSearchExplainCodeDTO(recordModel, originalQuery, localCoreRagBO,
                    taskContextModel.getRequest());
            chatFlux = localCoreService.deepSearchExplainCode(deepSearchExplainCodeDTO).share();
        } else {
            chatFlux = taskContextModel.getResult().share();
        }
        return chatFlux;
    }

    /**
     * 转换默认模型
     * @param request
     */
    private void changeDefaultModel(ChatRequestBean request) {
        // 如果当前使用的默认模型，本地仓库问答替换为drm配置的默认模型
        JSONObject defaultModel = JSONObject.parseObject(drmConfig.getCodebaseLocalDefaultModel());
        String modelName = (String) defaultModel.get("modelName");
        String modelType = (String) defaultModel.get("modelType");
        if (StringUtils.isBlank(request.getModelName()) || "Default".equalsIgnoreCase(request.getModelName())) {
            request.setModelName(modelName);
            request.setModelType(modelType);
        }
    }

    /**
     * 查询插件配置信息
     * 1: drm控制常见参数
     * 2: ob控制本地agent数据(有根据系统类型查询的步骤)
     * 3: drm控制插件侧滑块(后续删除这个drm,统一agent控制)
     *
     * @param request
     * @return
     */
    @Override
    public PluginConfigDataResponse queryConfigData(QueryPluginConfigRequestBean request) {
        if (StringUtil.isBlank(request.getUserToken()) || StringUtil.isBlank(request.getProductType())) {
            return new PluginConfigDataResponse(queryConfigService.queryPluginFile(request,null));
        }
        String cacheKey = QUERY_CONFIG_DATA_CACHE_PREFIX + request.getUserToken() + ":" + request.getProductType();
        PluginConfigDataResponse cache = cacheService.getCache(cacheKey, PluginConfigDataResponse.class);
        if (cache != null) {
            setInterValTimeForProductType(request, cache);
            return cache;
        }
        try {
            PluginConfigDataResponse pluginConfigDataResponse = JSONObject.parseObject(drmConfig.getProductPluginConfigData(),
                    PluginConfigDataResponse.class);
            if (pluginConfigDataResponse == null) {
                pluginConfigDataResponse = new PluginConfigDataResponse();
            }
            setInterValTimeForProductType(request, pluginConfigDataResponse);
            UserDO userDO = userService.queryUserToDO(request.getUserToken(),
                    EnumUtil.fromString(ProductTypeEnum.class, request.getProductType(), ProductTypeEnum.UNKNOWN));
            LOGGER.info("query config user: {}", JSONObject.toJSONString(userDO));
            if (userDO == null) {
                return new PluginConfigDataResponse(queryConfigService.queryPluginFile(request,null));
            }
            //设置tab切片，现在改用本地核心服务抽取，默认设置false兼容老版本
            pluginConfigDataResponse.setOpenSlidingWindows(false);

            //设置防抖时间，现在改用动态延迟，不再使用，写默认150兼容老版本插件
            pluginConfigDataResponse.setRequestDelayTime(150);

            // 动态时间参数范围
            pluginConfigDataResponse.setDebounceDefaultTime(drmConfig.getDebounceDefaultTime());

            // 判断是否是代码补全安全过滤白名单内，默认先关闭
            pluginConfigDataResponse.setEnableSecurityFilter(false);

            // 添加codeEdits配置
            addCodEditsConfig(request, userDO, pluginConfigDataResponse);
            // 添加功能权限
            addFunctionPermissions(userDO, pluginConfigDataResponse);

            //添加deepsearch权限
            addDeepSearchPermissions(userDO,pluginConfigDataResponse);

            pluginConfigDataResponse.setLocalFileList(queryConfigService.queryPluginFile(request,userDO));

            // 插件升级分流
            pluginUpgradeConfig(request, pluginConfigDataResponse, userDO);

            cacheService.putCache(cacheKey, pluginConfigDataResponse, CommonUtils.randomCacheTime(60 * 30, 60 * 30));
            cacheService.addSet(QUERY_CONFIG_DATA_CACHE_PREFIX + "ALL_KEY", cacheKey);
            return pluginConfigDataResponse;
        } catch (Throwable throwable) {
            LOGGER.error("queryConfigData error", throwable);
            return new PluginConfigDataResponse(queryConfigService.queryPluginFile(request,null));
        }
    }



    /**
     * 为不同的产品类型设置间隔时间
     *
     * @param request
     * @param cache
     */
    private void setInterValTimeForProductType(QueryPluginConfigRequestBean request, PluginConfigDataResponse cache) {
        if (ProductTypeEnum.VSCODE.name().equals(request.getProductType()) || ProductTypeEnum.CLOUD_IDE.name().equals(
                request.getProductType())) {
            cache.setIntervalTime(drmConfig.getVscodeIntervalTime());
        } else if (ProductTypeEnum.IDEA.name().equals(request.getProductType()) || ProductTypeEnum.CLOUD_IDE_IDEA.name().equals(
                request.getProductType())) {
            cache.setIntervalTime(drmConfig.getIdeaIntervalTime());
        }
    }

    /**
     * 对话基础校验
     * @param request
     * @param codegenChatRequestModel
     */
    private void baseCheck(ChatRequestBean request, CodegenChatRequestModel codegenChatRequestModel) {
        int systemPromptTokenNum = tokenizedService.getTokenNum(drmConfig.getTalkSystemInfo());
        int questionTokenNum = tokenizedService.getTokenNum(request.getQuestion());
        // 多预留100 避免调模型超长
        if (codegenChatRequestModel.getMaxTokens() - systemPromptTokenNum - questionTokenNum - 100 < 0) {
            // 基础校验 如果长度超过限制，直接抛异常让controller捕获
            throw new BizException(ResponseEnum.QUESTION_TOO_LONG);
        }
        // 网页版仓库问答基础校验
        if (KnowledgeTypeEnum.CODEBASE.name().equals(request.getKnowledgeType()) && StringUtils.isEmpty(request.getRepo())) {
            // 如果是仓库问答并且repo参数为空，那么直接返
            throw new BizException(ResponseEnum.BASE_CHECK_COMMON_ERR, AppConstants.REPO_ERROR_MSG_FOR_NOT_SUPPORT);
        }
    }

    /**
     * 添加codeEdits配置
     *
     * @param queryPluginConfigRequestBean
     * @param userDO
     * @param pluginConfigDataResponse
     */
    private void addCodEditsConfig(QueryPluginConfigRequestBean queryPluginConfigRequestBean, UserDO userDO,
                                   PluginConfigDataResponse pluginConfigDataResponse) {
        try {
            String enableCodeEdits = StringUtils.EMPTY;
            CodeEditsConfigBO codeEditsConfigBO = JSONObject.parseObject(drmConfig.getCodeEditsConfig()).toJavaObject(
                    CodeEditsConfigBO.class);
            // CODEFUSE_IDE全部放开不走分流了
            if (ProductTypeEnum.CODEFUSE_IDE.name().equals(queryPluginConfigRequestBean.getProductType())) {
                enableCodeEdits = FunctionPermissionsStatus.OPEN.name();
            }else {
                enableCodeEdits = abTestService.abTest(AbTestConstants.CODE_EDITS_ENABLE_AB,
                        CommonUtils.formatSassUserId(userDO.getSaasUserId()), CommonUtils.formatSassUserId(userDO.getSaasUserId()),
                        FunctionPermissionsStatus.CLOSE.name());
            }
            pluginConfigDataResponse.setEnableCodeEdits(FunctionPermissionsStatus.OPEN.name().equals(enableCodeEdits));
            pluginConfigDataResponse.setEditsCursorBefore(
                    null == codeEditsConfigBO.getEditsCursorBefore() ? 1 : codeEditsConfigBO.getEditsCursorBefore());
            pluginConfigDataResponse.setEditsCursorAfter(
                    null == codeEditsConfigBO.getEditsCursorAfter() ? 3 : codeEditsConfigBO.getEditsCursorAfter());
            pluginConfigDataResponse.setEditsCancelCount(
                    null == codeEditsConfigBO.getEditsCancelCount() ? 5 : codeEditsConfigBO.getEditsCancelCount());
            pluginConfigDataResponse.setEditsLastEditTimeDiff(
                    null == codeEditsConfigBO.getEditsLastEditTimeDiff() ? 60000 : codeEditsConfigBO.getEditsLastEditTimeDiff());
            pluginConfigDataResponse.setEditsResetCountTime(
                    null == codeEditsConfigBO.getEditsResetCountTime() ? 600000 : codeEditsConfigBO.getEditsResetCountTime());
            pluginConfigDataResponse.setEnableApplyTriggerEdits(codeEditsConfigBO.isEnableApplyTriggerEdits());
            pluginConfigDataResponse.setEditsTriggerDebounceTime(
                    null == codeEditsConfigBO.getEditsTriggerDebounceTime() ? 200 : codeEditsConfigBO.getEditsTriggerDebounceTime());
        } catch (Exception e) {
            LOGGER.error("codeEditsConfigBO parse error, request:{}", JSON.toJSONString(queryPluginConfigRequestBean), e);
        }
    }

    /**
     * 获取插件最新版本
     *
     * @param productTypeEnum
     * @return
     */
    @Override
    public String getPluginVersion(ProductTypeEnum productTypeEnum) {
        String pluginVersion = drmConfig.getPluginVersion();
        return Optional.ofNullable(pluginVersion).filter(StringUtils::isNotBlank).map(JSONObject::parseObject).map(
                jsonObject -> jsonObject.getString(productTypeEnum.name())).orElse(null);
    }

    /**
     * 获取提交消息
     *
     * @param commitMessageRequestBean
     * @param recordModel
     * @return
     */
    @Override
    public BaseResponse<BaseAnswerResponse> getCommitMessage(CommitMessageRequestBean commitMessageRequestBean,
                                                             InterfaceRecordModel recordModel) {
        String traceId = UUID.randomUUID().toString();
        //问答日志埋点
        EventLogUtils.recordLogForGetCommitMessage(commitMessageRequestBean,
                EnumUtil.fromString(ProductTypeEnum.class, commitMessageRequestBean.getProductType(), ProductTypeEnum.UNKNOWN), traceId);
        try {
            recordModel.setInterfaceInput(JSONObject.toJSONString(commitMessageRequestBean));
            String prompt = PromptDealUtils.transCommitMessagePrompt(commitMessageRequestBean.getOptionLanguage());
            LOGGER.info("commitMessage prompt: {}", prompt);
            String content = getContentFromDiffList(commitMessageRequestBean.getDiffList(), prompt);
            String question = prompt + content;
            //LOGGER.info("commitMessage question: {}", question);
            List<ChatMessageModel> chatMessages = new ArrayList<>(1);
            chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), question));
            AlgRequest<List<ChatMessageModel>> gptAlgRequest = new AlgRequest<>(chatMessages, drmConfig.getCodeGptModelConfig());
            ChatRequestServerModel serviceRequest = new ChatRequestServerModel(traceId, commitMessageRequestBean.getUserId(),
                    gptAlgRequest);
            serviceRequest.setProductType(
                    EnumUtil.fromString(ProductTypeEnum.class, commitMessageRequestBean.getProductType(), ProductTypeEnum.UNKNOWN));
            serviceRequest.setQuestionUid(traceId);
            serviceRequest.setExtended(new HashMap<String, Object>(4));
            serviceRequest.setIntention(IntentionEnum.COMMIT_MESSAGE_GENERATE);
            TaskContextModel<ChatRequestServerModel, String> taskContextModel = new TaskContextModel<>(serviceRequest);
            taskContextModel.setRecord(recordModel);
            codeChatExecutor.execute(taskContextModel);
            String result = taskContextModel.getResult();
            return BaseResponse.build(new BaseAnswerResponse(result, traceId));
        } catch (BizException e) {
            return BaseResponse.build(e.getErrorType().getErrorCode(), e.getMessage(), new BaseAnswerResponse(null, traceId));
        }
    }

    /**
     * 意图识别
     *
     * @param intentionRecognitionRequestBean
     * @param recordModel
     * @return
     */
    @Override
    public BaseResponse<BaseAnswerResponse> intentionRecognition(IntentionRecognitionRequestBean intentionRecognitionRequestBean,
                                                                 InterfaceRecordModel recordModel) {
        String traceId = UUID.randomUUID().toString();
        try {
            // prompt
            StringBuilder promptStr = new StringBuilder(drmConfig.getIntentionRecognitionPrompt());
            promptStr.append(intentionRecognitionRequestBean.getQuestion());
            LOGGER.info("意图识别prompt: {}", promptStr);
            // 判断是否超过最大token
            isMaxToken(promptStr);
            // 优先走规则匹配
            String result = intentionMatching(intentionRecognitionRequestBean.getQuestion());
            // 如果匹配失败，则走模型服务
            if (StringUtils.isBlank(result)) {
                LOGGER.info("意图识别走模型服务");
                List<ChatMessageModel> chatMessages = new ArrayList<>(1);
                chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), promptStr.toString()));
                AlgRequest<List<ChatMessageModel>> gptAlgRequest = new AlgRequest<>(chatMessages, drmConfig.getCodeGptModelConfig());
                ChatRequestServerModel serviceRequest = new ChatRequestServerModel(traceId, intentionRecognitionRequestBean.getUserId(),
                        gptAlgRequest);
                serviceRequest.setProductType(EnumUtil.fromString(ProductTypeEnum.class, intentionRecognitionRequestBean.getProductType(),
                        ProductTypeEnum.UNKNOWN));
                serviceRequest.setQuestionUid(traceId);
                serviceRequest.setExtended(new HashMap<String, Object>(4));
                serviceRequest.setIntention(IntentionEnum.INTENTION_RECOGNITION);
                TaskContextModel<ChatRequestServerModel, String> taskContextModel = new TaskContextModel<>(serviceRequest);
                taskContextModel.setRecord(recordModel);
                codeChatExecutor.execute(taskContextModel);
                result = taskContextModel.getResult();
            }
            return BaseResponse.build(new BaseAnswerResponse(result, traceId));
        } catch (BizException e) {
            return BaseResponse.build(e.getErrorType().getErrorCode(), e.getMessage(), new BaseAnswerResponse(null, traceId));
        }

    }

    @Override
    public BaseResponse<BaseAnswerResponse> codeGenerate(CodeGenerateRequestBean codeGenerateRequestBean,
                                                         InterfaceRecordModel recordModel) {
        String traceId = UUID.randomUUID().toString();
        try {
            recordModel.setInterfaceInput(JSONObject.toJSONString(codeGenerateRequestBean));
            StringBuilder prompt = new StringBuilder(drmConfig.getCodeGeneratePrompt());
            prompt.append("用户需求：").append(PromptDealUtils.transCodeGeneratePrompt(codeGenerateRequestBean.getQuestion(),
                    codeGenerateRequestBean.getOptionLanguage())).append(AppConstants.ENTER_SYMBOL);
            prompt.append("代码片段：").append(codeGenerateRequestBean.getQuestionCode()).append(AppConstants.ENTER_SYMBOL);
            //判断token是否符合
            isMaxToken(prompt);
            List<ChatMessageModel> chatMessages = new ArrayList<>(1);
            chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), prompt.toString()));
            AlgRequest<List<ChatMessageModel>> gptAlgRequest = new AlgRequest<>(chatMessages, drmConfig.getCodeGptModelConfig());
            ChatRequestServerModel serviceRequest = new ChatRequestServerModel(traceId, codeGenerateRequestBean.getUserId(), gptAlgRequest);
            serviceRequest.setProductType(
                    EnumUtil.fromString(ProductTypeEnum.class, codeGenerateRequestBean.getProductType(), ProductTypeEnum.UNKNOWN));
            serviceRequest.setQuestionUid(traceId);
            serviceRequest.setExtended(new HashMap<String, Object>(4));
            serviceRequest.setIntention(IntentionEnum.CODE_GENERATE);
            serviceRequest.setQuestionCode(codeGenerateRequestBean.getQuestionCode());
            TaskContextModel<ChatRequestServerModel, String> taskContextModel = new TaskContextModel<>(serviceRequest);
            taskContextModel.setRecord(recordModel);
            codeChatExecutor.execute(taskContextModel);
            String result = taskContextModel.getResult();
            return BaseResponse.build(new BaseAnswerResponse(result, traceId));
        } catch (BizException e) {
            return BaseResponse.build(e.getErrorType().getErrorCode(), e.getMessage(), new BaseAnswerResponse(null, traceId));
        } catch (Throwable e) {
            return BaseResponse.build(ResponseEnum.SYSTEM_ERROR.getErrorCode(), ResponseEnum.SYSTEM_ERROR.getErrorMsg(),
                    new BaseAnswerResponse(null, traceId));
        }
    }

    @Override
    public BaseResponse<List<String>> composerRagSearch(ComposerRequestBean composerRequestBean) {
        String traceId = UUID.randomUUID().toString();
        try {
            FunctionOnStreamRequestBean requestBean = new FunctionOnStreamRequestBean();
            InterfaceRecordModel recordModel = new InterfaceRecordModel(ServiceConstant.COMPOSER_RAG_SEARCH);
            functionOnStreamService.composerRagSearchParamsDeal(requestBean, traceId, composerRequestBean);
            recordModel.setInterfaceInput(JSONObject.toJSONString(composerRequestBean));
            String systemPrompt = PromptConstants.COMPOSER_RAG_SEARCH_SYSTEM_PROMPT;
            String userPrompt = PromptDealUtils.buildComposerRagSearchUserPrompt(PromptConstants.COMPOSER_RAG_SEARCH_USER_PROMPT,
                    requestBean.getReferenceList(), requestBean.getQuestion());
            //判断token是否符合
            List<ChatMessageModel> chatMessages = new ArrayList<>(2);
            chatMessages.add(new ChatMessageModel(ChatRoleEnum.SYSTEM.getAlgRequestName(), systemPrompt));
            chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), userPrompt));
            AlgRequest<List<ChatMessageModel>> gptAlgRequest = new AlgRequest<>(chatMessages, drmConfig.getCodeGptModelConfig());
            ChatRequestServerModel serviceRequest = buildComposerRagSearchRequestServerModel(traceId, requestBean, gptAlgRequest);
            TaskContextModel<ChatRequestServerModel, String> taskContextModel = new TaskContextModel<>(serviceRequest);
            taskContextModel.setRecord(recordModel);
            codeChatExecutor.execute(taskContextModel);
            String result = taskContextModel.getResult();
            List<String> resultList;
            try {
                resultList = JSONObject.parseArray(result).toJavaList(String.class);
            } catch (Throwable e) {
                LOGGER.warn("composerRagSearch result parse error, result: {}", result, e);
                resultList = new ArrayList<>();
            }
            return BaseResponse.buildWithTraceId(resultList, traceId);
        } catch (InvalidReqException invalidReqException) {
            return BaseResponse.buildWithTraceId(invalidReqException.getResponseEnum().getErrorCode(),
                    invalidReqException.getResponseEnum().getErrorMsg(), Collections.emptyList(), traceId);
        } catch (BizException bizException) {
            return BaseResponse.buildWithTraceId(bizException.getErrorType().getErrorCode(), bizException.getErrorType().getErrorMsg(),
                    Collections.emptyList(), traceId);
        } catch (Throwable throwable) {
            return BaseResponse.buildWithTraceId(ResponseEnum.SYSTEM_ERROR.getErrorCode(), ResponseEnum.SYSTEM_ERROR.getErrorMsg(),
                    Collections.emptyList(), traceId);
        }
    }

    /**
     * 获取请求模型对象
     * @param traceId
     * @param requestBean
     * @param gptAlgRequest
     * @return
     */
    private ChatRequestServerModel buildComposerRagSearchRequestServerModel(String traceId, FunctionOnStreamRequestBean requestBean,
                                                                    AlgRequest<List<ChatMessageModel>> gptAlgRequest) {
        ChatRequestServerModel serviceRequest = new ChatRequestServerModel(traceId, requestBean.getUserId(), gptAlgRequest);
        serviceRequest.setProductType(
                EnumUtil.fromString(ProductTypeEnum.class, requestBean.getProductType(), ProductTypeEnum.UNKNOWN));
        serviceRequest.setQuestionUid(traceId);
        serviceRequest.setExtended(new HashMap<>(4));
        serviceRequest.setIntention(IntentionEnum.COMPOSER_RAG_SEARCH);
        serviceRequest.setModelName(requestBean.getModelName());
        serviceRequest.setModelType(requestBean.getModelType());
        return serviceRequest;
    }

    /**
     * text2code流式接口
     *
     * @param request
     * @param recordModel
     * @return
     */
    @Override
    public Flux<? extends Object> functionOnStream(FunctionOnStreamRequestBean request, InterfaceRecordModel recordModel) {
        LOGGER.info("codeFuseServiceImpl functionOnStream,traceId:{},request: {}", request.getTraceId(), JSONObject.toJSONString(request));
        MayaStreamIntentionService mayaStreamIntentionService = mayaStreamIntentionFactory.getStrategy(request.getIntention());
        //问题入库
        mayaStreamIntentionService.saveQuestionToDatabase(request);
        //必须放到入库后面，composer入库的时候会有checkPoint情况，会新增一个preQuestionUid参数的赋值
        recordModel.setInterfaceInput(JSONObject.toJSONString(request));
        //策略工厂根据意图构建prompt
        String userPrompt = mayaStreamIntentionService.buildPrompt(request);
        //系统提示词
        String systemPrompt = mayaStreamIntentionService.getSystemPrompt(request);
        //检查token长度
        //checkTokenLength(request, prompt);
        List<ChatMessageModel> chatMessages = new ArrayList<>();
        if (null != systemPrompt) {
            //系统prompt
            chatMessages.add(new ChatMessageModel(ChatRoleEnum.SYSTEM.getAlgRequestName(), systemPrompt));
        }
        List<ChatMessageModel> chatMessageList = mayaStreamIntentionService.getChatMessageList(request);
        if (CollectionUtils.isNotEmpty(chatMessageList)) {
            //历史消息（0，1，2，3，4，5 顺序）
            chatMessages.addAll(chatMessageList);
        }
        //用户prompt
        chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), userPrompt));
        AlgRequest<List<ChatMessageModel>> gptAlgRequest = new AlgRequest<>(chatMessages, drmConfig.getCodeGptModelConfig());
        MayaStreamRequestServerModel serviceRequest = buildMayaStreamChatRequestServerModel(request, gptAlgRequest);
        TaskContextModel<MayaStreamRequestServerModel, Flux<MayaStreamResponseModel>> taskContextModel = new TaskContextModel<>(
                serviceRequest);
        taskContextModel.setRecord(recordModel);
        mayaStreamExecutor.execute(taskContextModel);
        Flux<MayaStreamResponseModel> chatFlux = taskContextModel.getResult().share();
        Mono<String> algResult = chatFlux.map(MayaStreamResponseModel::getContent).reduce(new StringBuilder(), StringBuilder::append).map(
                StringBuilder::toString);
        //将拼接后的字符串打印日志
        algResult.subscribe(result -> {
            //更新答案
            mayaStreamIntentionService.updateAnswer(request.getAnswerUid(), result, taskContextModel.getRequest().getExtended());
            recordModel.setAlgEndTime(System.currentTimeMillis());
            recordModel.setAlgOutput(result);
            recordModel.setInterfaceResult(result);
            //问答日志埋点
            CHAT_LOGGER.info(Base64Encoder.encode(JSONObject.toJSONString(recordModel)));
            LOGGER.info(JSONObject.toJSONString(recordModel));
            if (IntentionEnum.COMPOSER == request.getIntention() || IntentionEnum.FAST_APPLY == request.getIntention()) {
                AI_PARTNER_LOGGER.info(JSONObject.toJSONString(recordModel));
            }
        });
        return chatFlux;
    }

    /**
     * 检查token长度
     *
     * @param request
     * @param prompt
     */
    private void checkTokenLength(FunctionOnStreamRequestBean request, String prompt) {
        CodeGptModelConfig codeGptModelConfig = getCodeGptModelConfig(request.getIntention());
        Integer promptMaxToken = null != codeGptModelConfig.getPromptMaxToken() ? codeGptModelConfig.getPromptMaxToken() : 32000;
        //如果长度超过限制，直接返回
        if (null != codeGptModelConfig && tokenizedService.getTokenNum(prompt) > promptMaxToken) {
            LOGGER.warn("prompt length is too long, promptMaxToken:{}, traceId:{}, sessionId:{}", promptMaxToken, request.getTraceId(),
                    request.getSessionId());
            throw new BizException(ResponseEnum.QUESTION_TOO_LONG, ResponseEnum.QUESTION_TOO_LONG.getErrorMsg());
        }
    }

    /**
     * query改写
     *
     * @param: [baseAgentRequestBean, recordModel]
     * @return: com.alipay.tsingyanprod.model.response.BaseResponse<com.alipay.tsingyanprod.model.response.BaseAnswerResponse>
     */
    @Override
    public BaseResponse<BaseAnswerResponse<Map<String, Object>>> queryChange(BaseAgentRequestBean baseAgentRequestBean,
                                                                             InterfaceRecordModel recordModel) {
        String traceId = UUID.randomUUID().toString();
        try {
            String result = getLocalAgentRepoCodeChatResult(baseAgentRequestBean, recordModel, traceId, IntentionEnum.QUERY_CHANGE);
            return BaseResponse.build(BaseAnswerResponse.build(JSON.parseObject(result).toJavaObject(Map.class), traceId));
        } catch (BizException e) {
            LOGGER.warn("queryChange biz error", e);
            return BaseResponse.build(e.getErrorType().getErrorCode(), e.getMessage(), BaseAnswerResponse.buildEmpty(traceId));
        } catch (Throwable e) {
            LOGGER.warn("queryChange throwable error", e);
            return BaseResponse.build(ResponseEnum.SYSTEM_ERROR.getErrorCode(), ResponseEnum.SYSTEM_ERROR.getErrorMsg(),
                    BaseAnswerResponse.buildEmpty(traceId));
        }
    }

    /**
     * 代码总结
     *
     * @param: [baseAgentRequestBean, recordModel]
     * @return: com.alipay.tsingyanprod.model.response.BaseResponse<com.alipay.tsingyanprod.model.response.BaseAnswerResponse>
     */
    @Override
    public BaseResponse<BaseAnswerResponse<Map<String, Object>>> codeSummary(LocalRepoRequestDTO requestBean,
                                                                             InterfaceRecordModel recordModel) {
        String traceId = UUID.randomUUID().toString();
        try {
            if (CollectionUtils.isNotEmpty(requestBean.getCodeList())) {
                List<String> codeList = requestBean.getCodeList();
                Map<String, Object> resultListMap = new HashMap<>(8);
                List<List<String>> summaryList = new ArrayList<>(10);
                List<List<String>> keywordsList = new ArrayList<>(10);
                for (String code : codeList) {
                    requestBean.setQuestion(code);
                    String result = getLocalAgentRepoCodeChatResult(requestBean, recordModel, traceId, IntentionEnum.CODE_SUMMARY);
                    Map resultMap = JSON.parseObject(result).toJavaObject(Map.class);
                    List<String> summaryItemList = (List<String>) resultMap.get("summary");
                    List<String> keywordsItemList = (List<String>) resultMap.get("keywords");
                    summaryList.add(null == summaryItemList ? Collections.emptyList() : summaryItemList);
                    keywordsList.add(null == keywordsItemList ? Collections.emptyList() : keywordsItemList);
                }
                resultListMap.put("summaryList", summaryList);
                resultListMap.put("keywordsList", keywordsList);
                return BaseResponse.build(BaseAnswerResponse.build(resultListMap, traceId));
            }
            String result = getLocalAgentRepoCodeChatResult(requestBean, recordModel, traceId, IntentionEnum.CODE_SUMMARY);
            return BaseResponse.build(BaseAnswerResponse.build(JSON.parseObject(result).toJavaObject(Map.class), traceId));
        } catch (BizException e) {
            LOGGER.warn("codeSummary biz error", e);
            return BaseResponse.build(e.getErrorType().getErrorCode(), e.getMessage(), BaseAnswerResponse.buildEmpty(traceId));
        } catch (Throwable e) {
            LOGGER.warn("codeSummary throwable error", e);
            return BaseResponse.build(ResponseEnum.SYSTEM_ERROR.getErrorCode(), ResponseEnum.SYSTEM_ERROR.getErrorMsg(),
                    BaseAnswerResponse.buildEmpty(traceId));
        }
    }

    /**
     * 获取本地仓库问答LLM结果
     *
     * @param: [baseAgentRequestBean, recordModel, traceId]
     * @return: java.lang.String
     */
    private String getLocalAgentRepoCodeChatResult(BaseAgentRequestBean baseAgentRequestBean, InterfaceRecordModel recordModel,
                                                   String traceId, IntentionEnum intentionEnum) {
        recordModel.setInterfaceInput(JSONObject.toJSONString(baseAgentRequestBean));
        List<ChatMessageModel> chatMessages = new ArrayList<>(1);
        String question = baseAgentRequestBean.getQuestion();
        if (IntentionEnum.QUERY_CHANGE == intentionEnum) {
            question = "[question]: " + question + AppConstants.ENTER_SYMBOL;
        } else if (IntentionEnum.CODE_SUMMARY == intentionEnum) {
            question = String.format("Analyze the following code snippet:\n<code>\n%s\n</code>", question);
        }
        chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), question));
        AlgRequest<List<ChatMessageModel>> gptAlgRequest = new AlgRequest<>(chatMessages, drmConfig.getCodeGptModelConfig());
        ChatRequestServerModel serviceRequest = new ChatRequestServerModel(traceId, baseAgentRequestBean.getUserId(), gptAlgRequest);
        serviceRequest.setProductType(
                EnumUtil.fromString(ProductTypeEnum.class, baseAgentRequestBean.getProductType(), ProductTypeEnum.UNKNOWN));
        serviceRequest.setQuestionUid(traceId);
        serviceRequest.setExtended(new HashMap<String, Object>(4));
        serviceRequest.setIntention(intentionEnum);
        TaskContextModel<ChatRequestServerModel, String> taskContextModel = new TaskContextModel<>(serviceRequest);
        taskContextModel.setRecord(recordModel);
        localRepoCodeChatExecutor.execute(taskContextModel);
        return taskContextModel.getResult();
    }

    /**
     * 合并测试用例
     *
     * @param: [mergeTestCaseRequestBean]
     * @return: com.alipay.tsingyanprod.model.response.BaseResponse<com.alipay.tsingyanprod.model.response.MergeTestCaseResponse>
     */
    @Override
    public MergeTestCaseResponse mergeTestCase(MergeTestCaseRequestBean request) {
        TaskContextModel<MergeTestCaseRequestBean, MergeTestCaseResponse> taskContextModel = new TaskContextModel<>(request);
        MergeTestCaseResponse mergeTestCaseResponse = new MergeTestCaseResponse();
        taskContextModel.setResult(mergeTestCaseResponse);
        testCaseExecutor.execute(taskContextModel);
        return taskContextModel.getResult();
    }

    /**
     * 索引构建进度（接口转发）
     *
     * @param requestBean
     * @return
     */
    @Override
    public BaseResponse<BaseAnswerResponse<Object>> indexBuildProgress(BaseRepoRequestBean requestBean) {
        String traceId = UUID.randomUUID().toString();
        //创建埋点记录对象
        InterfaceRecordModel recordModel = InterfaceRecordModelUtils.createInterfaceRecordModel(ServiceConstant.INDEX_BUILD_PROGRESS,
                JSONObject.toJSONString(requestBean));
        try {
            String url = CodeUtils.getValueByKeyFromDrmStringValue("url", drmConfig.getIndexBuildProgressUrl());
            Map<String, String> headers = CommonUtils.getCodegencoreRequestBaseHeader(drmConfig.getIndexBuildProgressUrl());
            if (null == headers) {
                throw new BizException(ResponseEnum.ALG_PARAMS_ERROR, ResponseEnum.ALG_PARAMS_ERROR.getErrorMsg());
            }
            StringBuilder urlBuilder = new StringBuilder(url);
            urlBuilder.append("?repoURL=").append(URLEncoder.encode(requestBean.getRepo(), "UTF-8"));
            urlBuilder.append("&branch=").append(URLEncoder.encode(requestBean.getBranch(), "UTF-8"));
            String requestUrl = urlBuilder.toString();
            // 设置日志算法请求对象
            recordModel.setAlgRequestInfo(JSON.toJSONString(Map.of("url", requestUrl, "traceId", traceId)), null);
            String res = HttpUtil.get(requestUrl, headers, 30000);
            recordModel.setAlgResponseInfo(res);
            JSONObject result = JSON.parseObject(res);
            int errorCode = result.getInteger("errorCode");
            String algTraceId = Optional.ofNullable(result.getString("traceId")).orElse(StringUtils.EMPTY);
            BaseResponse<BaseAnswerResponse<Object>> finallyResult = null;
            if (0 == errorCode) {
                JSONObject data = result.getJSONObject("data");
                finallyResult = BaseResponse.build(BaseAnswerResponse.build(data.toJavaObject(Object.class), traceId, List.of(algTraceId)));
            }
            InterfaceRecordModelUtils.setInterfaceResult(recordModel, JSON.toJSONString(finallyResult));
            //LOGGER.info(JSONObject.toJSONString(recordModel));
            CHAT_LOGGER.info(Base64Encoder.encode(JSONObject.toJSONString(recordModel)));
            if (null != finallyResult) {
                return finallyResult;
            }
            String errorMsg = result.getString("errorMsg");
            LOGGER.warn("indexBuildProgress error,requestBean:{} errorCode:{},errorMsg:{},traceId:{},algResult:{}",
                    JSON.toJSONString(requestBean), errorCode, errorMsg, traceId, res);
            return BaseResponse.build(errorCode, errorMsg, BaseAnswerResponse.build(null, traceId, List.of(algTraceId)));
        } catch (BizException e) {
            LOGGER.warn("indexBuildProgress bizError, request:" + JSON.toJSONString(requestBean), e);
            InterfaceRecordModelUtils.setInterfaceResult(recordModel, null);
            //LOGGER.info(JSONObject.toJSONString(recordModel));
            CHAT_LOGGER.info(Base64Encoder.encode(JSONObject.toJSONString(recordModel)));
            return BaseResponse.build(e.getErrorType().getErrorCode(), e.getMessage(), BaseAnswerResponse.buildEmpty(traceId));
        } catch (Throwable e) {
            LOGGER.warn("indexBuildProgress error, request:" + JSON.toJSONString(requestBean), e);
            InterfaceRecordModelUtils.setInterfaceResult(recordModel, null);
            //LOGGER.info(JSONObject.toJSONString(recordModel));
            CHAT_LOGGER.info(Base64Encoder.encode(JSONObject.toJSONString(recordModel)));
            return BaseResponse.build(ResponseEnum.SYSTEM_ERROR.getErrorCode(), ResponseEnum.SYSTEM_ERROR.getErrorMsg(),
                    BaseAnswerResponse.buildEmpty(traceId));
        }
    }

    /**
     * 语雀文档分段信息查询
     *
     * @param requestBean
     * @return
     */
    @Override
    public BaseResponse<BaseAnswerResponse<List<SegmentInfoResponse>>> docSearch(SegmentInfosRequestBean requestBean) {
        String traceId = UUID.randomUUID().toString();
        String res = null;
        try {
            String url = CodeUtils.getValueByKeyFromDrmStringValue("url", drmConfig.getYuQueDocSearchUrl());
            Map<String, String> headers = CommonUtils.getCodegencoreRequestBaseHeader(drmConfig.getYuQueDocSearchUrl());
            if (null == headers) {
                throw new BizException(ResponseEnum.ALG_PARAMS_ERROR, ResponseEnum.ALG_PARAMS_ERROR.getErrorMsg());
            }
            StringBuilder urlBuilder = new StringBuilder(url);
            urlBuilder.append("?query=").append(URLEncoder.encode(requestBean.getQuestion(), "UTF-8"));
            urlBuilder.append("&sceneId=").append(URLEncoder.encode(String.valueOf(requestBean.getSceneId()), "UTF-8"));
            if (null != requestBean.getLimit() && requestBean.getLimit() > 0) {
                urlBuilder.append("&limit=").append(URLEncoder.encode(String.valueOf(requestBean.getLimit()), "UTF-8"));
            }
            String requestUrl = urlBuilder.toString();
            res = HttpUtil.get(requestUrl, headers, 30000);
            JSONObject result = JSON.parseObject(res);
            String algTraceId = Optional.ofNullable(result.getString("traceId")).orElse(StringUtils.EMPTY);
            int errorCode = result.getInteger("errorCode");
            if (0 == errorCode) {
                List<SegmentInfoResponse> segmentInfoResponses = result.getJSONArray("data").toJavaList(SegmentInfoResponse.class);
                return BaseResponse.build(BaseAnswerResponse.build(segmentInfoResponses, traceId, List.of(algTraceId)));
            }
            String errorMsg = result.getString("errorMsg");
            return BaseResponse.build(errorCode, errorMsg, BaseAnswerResponse.build(null, traceId, List.of(algTraceId)));
        } catch (BizException e) {
            LOGGER.warn("segmentInfos bizError, request:{}", JSON.toJSONString(requestBean), e);
            return BaseResponse.build(e.getErrorType().getErrorCode(), e.getMessage(), BaseAnswerResponse.buildEmpty(traceId));
        } catch (Throwable e) {
            LOGGER.warn("segmentInfos error, request:{},response:{},traceId:{}", JSON.toJSONString(requestBean), res, traceId, e);
            return BaseResponse.build(ResponseEnum.SYSTEM_ERROR.getErrorCode(), ResponseEnum.SYSTEM_ERROR.getErrorMsg(),
                    BaseAnswerResponse.buildEmpty(traceId));
        }
    }

    /**
     * 仓库问答问题推荐
     *
     * @param requestBean
     * @return
     */
    @Override
    public BaseResponse<BaseAnswerResponse<List<List<QuestionRecommendResponse>>>> repoChatQuestionRecommend(
            BaseRepoRequestBean requestBean) {
        if (null == requestBean.getRepo() || !requestBean.getRepo().trim().startsWith("http")) {
            return BaseResponse.build(BaseAnswerResponse.build(Collections.EMPTY_LIST, null));
        }
        //创建埋点记录对象
        InterfaceRecordModel recordModel = InterfaceRecordModelUtils.createInterfaceRecordModel(ServiceConstant.QUESTION_RECOMMEND,
                JSONObject.toJSONString(requestBean));
        //初始化必要对象
        String traceId = UUID.randomUUID().toString();
        String jsonResult = null;
        String algTraceId = StringUtils.EMPTY;
        BaseResponse<BaseAnswerResponse<List<List<QuestionRecommendResponse>>>> res = null;
        //开始主要逻辑
        try {
            // 获取请求url
            String url = CodeUtils.getValueByKeyFromDrmStringValue("url", drmConfig.getRepoChatQuestionRecommendUrl());
            // 设置请求头
            Map<String, String> headers = CommonUtils.getCodegencoreRequestBaseHeader(drmConfig.getRepoChatQuestionRecommendUrl());
            // 设置请求体
            Map<String, Object> body = Map.of("repo_url", requestBean.getRepo(), "branch", requestBean.getBranch());
            StringEntity postEntity = new StringEntity(JSONObject.toJSONString(body), "UTF-8");
            postEntity.setContentType("application/json");
            // 设置日志算法请求对象
            recordModel.setAlgRequestInfo(JSON.toJSONString(Map.of("url", url, "body", body, "traceId", traceId)), null);
            // 发起请求
            jsonResult = HttpUtil.post(url, postEntity, headers, 30000);
            // 记录算法请求结果信息
            recordModel.setAlgResponseInfo(jsonResult);
            // 对结果进行解析
            JSONObject result = JSON.parseObject(jsonResult);
            if (0 == result.getInteger("errorCode")) {
                List<List<QuestionRecommendResponse>> resultList = questionRecommendAfterHandle(jsonResult).stream().map(
                        question -> List.of(new QuestionRecommendResponse(question))).collect(Collectors.toList());
                algTraceId = result.getString("traceId");
                res = BaseResponse.build(new BaseAnswerResponse(resultList, traceId, List.of(algTraceId)));
            }
            InterfaceRecordModelUtils.setInterfaceResult(recordModel, JSON.toJSONString(res));
            //LOGGER.info(JSONObject.toJSONString(recordModel));
            CHAT_LOGGER.info(Base64Encoder.encode(JSONObject.toJSONString(recordModel)));
            if (null == res) {
                // 异常情况
                LOGGER.warn("repoChatQuestionRecommend service error，request:{}, response:{}, traceId:{}, algTraceId:{}, errorMsg:{}",
                        JSON.toJSONString(requestBean), jsonResult, traceId, algTraceId, result.getString("errorMsg"));
                return BaseResponse.build(BaseAnswerResponse.build(Collections.emptyList(), traceId, List.of(algTraceId)));
            }
            return res;
        } catch (Throwable t) {
            InterfaceRecordModelUtils.setAlgAndInterfaceResult(recordModel, jsonResult, JSON.toJSONString(res));
            //LOGGER.info(JSONObject.toJSONString(recordModel));
            CHAT_LOGGER.info(Base64Encoder.encode(JSONObject.toJSONString(recordModel)));
            LOGGER.warn(
                    "repoChatQuestionRecommend service throwable error，request:{}, response:{}, traceId:{}, algTraceId:{}, algResult:{}",
                    JSON.toJSONString(requestBean), jsonResult, traceId, algTraceId, jsonResult, t);
            return BaseResponse.build(BaseAnswerResponse.build(Collections.emptyList(), traceId, List.of(algTraceId)));
        }
    }

    /**
     * 仓库列表
     *
     * @param requestBean
     * @return
     */
    @Override
    public BaseResponse<BaseAnswerResponse<List<RepoResponse>>> repoList(RepoListRequestBean requestBean) {
        String traceId = UUID.randomUUID().toString();
        if (null == requestBean.getApplyFrom()) {
            LOGGER.info("repoList request applyFrom is null, request:{},traceId:{}", JSON.toJSONString(requestBean), traceId);
            return BaseResponse.build(BaseAnswerResponse.build(Collections.EMPTY_LIST, traceId));
        }
        JSONObject requestConfig = Optional.ofNullable(
                JSONObject.parseObject(drmConfig.getRepoListRequestConfig()).getJSONObject(requestBean.getApplyFrom().name())).orElse(null);
        if (null == requestConfig) {
            LOGGER.info("repoList request requestConfig is null, request:{},traceId:{}", JSON.toJSONString(requestBean), traceId);
            return BaseResponse.build(BaseAnswerResponse.build(Collections.EMPTY_LIST, traceId));
        }
        //创建埋点记录对象
        InterfaceRecordModel recordModel = InterfaceRecordModelUtils.createInterfaceRecordModel(ServiceConstant.REPO_LIST,
                JSONObject.toJSONString(requestBean));
        //初始化必要对象
        String jsonResult = null;
        BaseResponse<BaseAnswerResponse<List<RepoResponse>>> res = null;
        //开始主要逻辑
        try {
            // 目前只支持伙伴应用的仓库列表查询
            if (ApplyFromEnum.HUO_BAN_APP == requestBean.getApplyFrom()) {
                // 获取请求url
                String url = requestConfig.getString("url");
                // 组装请求url
                String requestUrl = buildRepoListRequestUrl(url, requestConfig);
                // 设置日志算法请求对象
                recordModel.setAlgRequestInfo(JSON.toJSONString(Map.of("url", url, "traceId", traceId)), null);
                // 发起请求
                jsonResult = HttpUtil.get(requestUrl, null, 30000);
                // 记录算法请求结果信息
                recordModel.setAlgResponseInfo(jsonResult);
                // 对结果进行解析
                JSONObject result = JSON.parseObject(jsonResult);
                if (result.getBoolean("success")) {
                    res = getRepoListResult(result, traceId);
                }
                InterfaceRecordModelUtils.setInterfaceResult(recordModel, JSON.toJSONString(res));
                //LOGGER.info(JSONObject.toJSONString(recordModel));
                CHAT_LOGGER.info(Base64Encoder.encode(JSONObject.toJSONString(recordModel)));
                if (null == res) {
                    // 异常情况
                    LOGGER.warn("repoList request service error，request:{}, response:{}, traceId:{}, errorMsg:{}",
                            JSON.toJSONString(requestBean), jsonResult, traceId, result.getString("errMsg"));
                    return BaseResponse.build(BaseAnswerResponse.build(Collections.emptyList(), traceId));
                }
                return res;
            }
            LOGGER.warn("repoList request applyFrom error，request:{}, response:{}, traceId:{}", JSON.toJSONString(requestBean), jsonResult,
                    traceId);
            return BaseResponse.build(BaseAnswerResponse.build(Collections.emptyList(), traceId));
        } catch (Throwable t) {
            InterfaceRecordModelUtils.setAlgAndInterfaceResult(recordModel, jsonResult, JSON.toJSONString(res));
            //LOGGER.info(JSONObject.toJSONString(recordModel));
            CHAT_LOGGER.info(Base64Encoder.encode(JSONObject.toJSONString(recordModel)));
            LOGGER.warn("repoChatQuestionRecommend service throwable error，request:{}, response:{}, traceId:{}, algResult:{}",
                    JSON.toJSONString(requestBean), jsonResult, traceId, jsonResult, t);
            return BaseResponse.build(BaseAnswerResponse.build(Collections.emptyList(), traceId));
        }
    }

    /**
     * 获取模型列表信息
     *
     * @param requestBean
     * @return
     */
    @Override
    public BaseResponse<BaseAnswerResponse<List<AlgModelResponseBean>>> getAlgModelList(ModelListRequestBean requestBean) {
        String traceId = UUID.randomUUID().toString();
        InterfaceRecordModel recordModel = new InterfaceRecordModel(ServiceConstant.GET_MODEL_PERMISSION);
        recordModel.setInterfaceInput(JSONObject.toJSONString(requestBean));
        recordModel.setStartTime(System.currentTimeMillis());
        List<AlgModelResponseBean> resultList = new ArrayList<>(10);
        LOGGER.info("getAlgModelList traceId: {},param: {}", traceId, JSONObject.toJSONString(requestBean));
        try {
            ProductTypeEnum productTypeEnum = EnumUtil.fromString(ProductTypeEnum.class, requestBean.getProductType(),
                    ProductTypeEnum.UNKNOWN);
            UserDO userDO = queryUserByProductAndToken(productTypeEnum, requestBean.getUserToken());
            // AI程序员
            if (FunctionPermissionsEnum.AI_PARTNER.name().equalsIgnoreCase(requestBean.getTabType())) {
                List<AlgModelResponseBean> modelList = JSONObject.parseArray(drmConfig.getAiPartnerModelList()).toJavaList(
                        AlgModelResponseBean.class);
                addModelPermission(modelList, userDO, resultList, "AI_PARTNER_MODEL_PERMISSION_");
                return BaseResponse.build(BaseAnswerResponse.build(resultList, traceId));
            }
            // 智能问答
            else if (FunctionPermissionsEnum.AI_CHAT.name().equalsIgnoreCase(requestBean.getTabType())) {
                List<AlgModelResponseBean> modelList = JSONObject.parseArray(drmConfig.getAiChatModelList()).toJavaList(
                        AlgModelResponseBean.class);
                addModelPermission(modelList, userDO, resultList, "AI_CHAT_MODEL_PERMISSION_");
                return BaseResponse.build(BaseAnswerResponse.build(resultList, traceId));
            }
            return modelDefaultPermission(resultList, traceId);
        } catch (InvalidReqException e) {
            LOGGER.warn("getAlgModelList invalidReqException, requestBean: {}", JSONObject.toJSONString(requestBean), e);
            return modelDefaultPermission(resultList, traceId);
        } catch (Throwable e) {
            LOGGER.warn("getAlgModelList throwable, requestBean: {}", JSONObject.toJSONString(requestBean), e);
            return modelDefaultPermission(resultList, traceId);
        } finally {
            recordModel.setInterfaceOutput(JSONObject.toJSONString(BaseAnswerResponse.build(resultList, traceId)));
            recordModel.setEndTime(System.currentTimeMillis());
            LOGGER.info(JSONObject.toJSONString(recordModel));
        }
    }

    /**
     * 模型默认权限
     *
     * @param resultList
     * @param traceId
     * @return
     */
    private BaseResponse<BaseAnswerResponse<List<AlgModelResponseBean>>> modelDefaultPermission(List<AlgModelResponseBean> resultList,
                                                                                                String traceId) {
        resultList.add(getDefaultModel());
        return BaseResponse.build(BaseAnswerResponse.build(resultList, traceId));
    }

    /**
     * 添加模型权限
     *
     * @param modelList
     * @param userDO
     * @param resultList
     */
    private void addModelPermission(List<AlgModelResponseBean> modelList, UserDO userDO, List<AlgModelResponseBean> resultList,
                                    String abKeyPrefix) {
        if (CollectionUtils.isEmpty(modelList)) {
            return;
        }
        String formattedSaasUserId = CommonUtils.formatSassUserId(userDO.getSaasUserId());
        modelList.stream().filter(model -> isModelAllowed(model, abKeyPrefix, formattedSaasUserId)).forEach(resultList::add);
    }

    /**
     * 判断模型是否允许
     *
     * @param model
     * @param abKeyPrefix
     * @param formattedSaasUserId
     * @return
     */
    private boolean isModelAllowed(AlgModelResponseBean model, String abKeyPrefix, String formattedSaasUserId) {
        String modelName = model.getModelName();
        if ("Default".equals(modelName)) {
            return true;
        }
        String abKey = abKeyPrefix + modelName;
        String itemPermission = abTestService.abTest(abKey, formattedSaasUserId, formattedSaasUserId,
                FunctionPermissionsStatus.CLOSE.name());
        return FunctionPermissionsStatus.OPEN.name().equals(itemPermission);
    }

    /**
     * 获取默认模型
     *
     * @return
     */
    private AlgModelResponseBean getDefaultModel() {
        AlgModelResponseBean algModelResponseBean = new AlgModelResponseBean();
        algModelResponseBean.setModelText("默认模型");
        algModelResponseBean.setModelName("Default");
        algModelResponseBean.setModelType(AlgModelTypeEnum.MAYA.name());
        return algModelResponseBean;
    }

    /**
     * 获取仓库列表结果
     *
     * @param result
     * @param traceId
     * @return
     */
    private BaseResponse<BaseAnswerResponse<List<RepoResponse>>> getRepoListResult(JSONObject result, String traceId) {
        try {
            List<RepoResponse> repoResponseList = new ArrayList<>();
            JSONArray data = result.getJSONArray("data");
            for (int i = 0; i < data.size(); i++) {
                JSONObject jsonObject = data.getJSONObject(i);
                String productName = jsonObject.getString("productName");
                List<Map<String, String>> platformList = JSONObject.parseObject(JSON.toJSONString(jsonObject.getJSONArray("platforms")),
                        new TypeReference<>() {});
                dealRepoListResult(platformList, productName, repoResponseList);
            }
            return BaseResponse.build(new BaseAnswerResponse(repoResponseList, traceId));
        } catch (Throwable e) {
            LOGGER.warn("getRepoListResult error，result:{}, traceId:{}", JSON.toJSONString(result), traceId, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理仓库列表结果
     *
     * @param platformList
     * @param productName
     * @param repoResponseList
     */
    private void dealRepoListResult(List<Map<String, String>> platformList, String productName, List<RepoResponse> repoResponseList) {
        for (int i = 0; i < platformList.size(); i++) {
            Map<String, String> map = platformList.get(i);
            //遍历map
            for (Map.Entry<String, String> entry : map.entrySet()) {
                RepoResponse repoResponse = new RepoResponse(productName + "-" + entry.getKey(), entry.getValue());
                repoResponseList.add(repoResponse);
            }
        }
    }

    /**
     * 构建仓库列表请求url
     *
     * @param url
     * @param requestConfig
     * @return
     */
    private String buildRepoListRequestUrl(String url, JSONObject requestConfig) {
        StringBuilder urlBuilder = null;
        try {
            urlBuilder = new StringBuilder(url);
            urlBuilder.append("?appid=").append(URLEncoder.encode(requestConfig.getString("appid"), "UTF-8"));
            urlBuilder.append("&apptoken=").append(URLEncoder.encode(requestConfig.getString("apptoken"), "UTF-8"));
            urlBuilder.append("&apikey=").append(URLEncoder.encode(requestConfig.getString("apikey"), "UTF-8"));
            return urlBuilder.toString();
        } catch (Throwable e) {
            LOGGER.warn("buildRepoListRequestUrl error，url:{}, requestConfig:{}, errorMsg:{}", url, JSON.toJSONString(requestConfig),
                    e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 针对仓库问答推荐问题列表后处理
     *
     * @param jsonResult
     */
    private List<String> questionRecommendAfterHandle(String jsonResult) {
        List<JSONObject> resultList = JSON.parseObject(jsonResult).getJSONArray("data").toJavaList(JSONObject.class);
        // 根据gmt_create时间倒序排列
        resultList.sort((o1, o2) -> o2.getString("gmt_create").compareTo(o1.getString("gmt_create")));

        // 获取第一个结果列表判断是否为空，为空则返回空集合，不为空则转换成String集合返回
        return resultList.stream().findFirst().map(result -> result.getString("recommend_questions")).filter(StringUtils::isNotBlank).map(
                recommendQuestions -> JSON.parseArray(recommendQuestions, String.class)).orElse(Collections.emptyList());
    }

    /**
     * 从diffList中获取信息
     *
     * @param diffList
     * @return
     */
    private String getContentFromDiffList(List<CommitDiffRequestBean> diffList, String prompt) {
        if (CollectionUtils.isEmpty(diffList)) {
            return StringUtils.EMPTY;
        }
        //查drm配置的prompt最大token数
        CodeGptModelConfig config = JSONObject.parseObject(drmConfig.getChatModelMayaConfig()).getJSONObject(
                IntentionEnum.COMMIT_MESSAGE_GENERATE.name()).toJavaObject(CodeGptModelConfig.class);
        Integer promptMaxToken = config.getPromptMaxToken();
        int tokenNum = tokenizedService.getTokenNum(prompt);
        //这里多减200防止超长
        int leftToken = promptMaxToken - tokenNum - 200;
        int avgToken = leftToken / diffList.size();

        // 使用 Map 来收集不同类型的内容
        Map<CommitDiffTypeEnum, List<String>> dataMap = new EnumMap<>(CommitDiffTypeEnum.class);
        // 初始化 Map
        for (CommitDiffTypeEnum type : CommitDiffTypeEnum.values()) {
            dataMap.put(type, new ArrayList<>());
        }
        // 分类内容
        for (int i = 0; i < diffList.size(); i++) {
            String content = diffList.get(i).getContent();
            CommitDiffTypeEnum status = diffList.get(i).getStatus();
            if (status == CommitDiffTypeEnum.MODIFIED && StringUtils.isNotBlank(content)) {
                int contentToken = tokenizedService.getTokenNum(content);
                if (contentToken > avgToken) {
                    //精简content
                    content = simplifyContent(content, contentToken, avgToken);
                    contentToken = tokenizedService.getTokenNum(content);
                }
                leftToken = leftToken - contentToken;
                if (leftToken < 0) {
                    break;
                }
                dataMap.get(status).add(content);
            } else {
                String filePath = diffList.get(i).getFilePath();
                if (StringUtils.isBlank(filePath)) {
                    continue;
                }
                int filePathToken = tokenizedService.getTokenNum(filePath);
                leftToken = leftToken - filePathToken;
                if (leftToken < 0) {
                    break;
                }
                dataMap.get(status).add(filePath);
            }
            //更新平均token，避免有些文件名过小浪费空间了
            int leftSize = diffList.size() - i - 1;
            if (leftSize > 1) {
                avgToken = leftToken / leftSize;
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        // 添加各类文件的内容
        appendContents(stringBuilder, "新增的文件内容如下：", dataMap.get(CommitDiffTypeEnum.ADDED));
        appendContents(stringBuilder, "删除的文件列表如下：", dataMap.get(CommitDiffTypeEnum.DELETED));
        appendContents(stringBuilder, "修改的文件列表如下：", dataMap.get(CommitDiffTypeEnum.MODIFIED));
        return stringBuilder.toString();
    }

    /**
     * 精简content
     *
     * @param content
     * @param contentToken
     * @param avgToken
     * @return
     */
    private String simplifyContent(String content, int contentToken, int avgToken) {
        String[] split = content.split(AppConstants.ENTER_SYMBOL);
        StringBuilder stringBuilder = new StringBuilder();
        for (String s : split) {
            if ((s.startsWith("package") || s.startsWith("import")) && s.endsWith(";")) {
                continue;
            }
            stringBuilder.append(s).append(AppConstants.ENTER_SYMBOL);
        }
        content = stringBuilder.toString();
        double percent = (double) avgToken / contentToken;
        int index = (int) (content.length() * percent);
        return content.substring(0, index);
    }

    /**
     * 组装内容
     *
     * @param stringBuilder
     * @param header
     * @param contents
     */
    private void appendContents(StringBuilder stringBuilder, String header, List<String> contents) {
        if (CollectionUtils.isNotEmpty(contents)) {
            stringBuilder.append(header).append(AppConstants.ENTER_SYMBOL);
            for (String content : contents) {
                stringBuilder.append(content).append(AppConstants.ENTER_SYMBOL);
            }
        }
    }

    /**
     * 将对话数据存储到数据库（空答案）
     *
     * @param request   请求参数
     * @param intention
     * @return <问题的msgUid,答案的msgUid>
     */
    private void saveDataToDatabase(ChatRequestBean request, ChatIntentionEnum intention, String originalQuestion) {
        //如果不是重试，需要存入问题
        int nextChatIndex = chatMessageService.getNextChatIndex(request.getSessionId(), request.isRetry());
        if (!request.isRetry()) {
            insertMessage(request.getSessionId(), request.getUserId(), originalQuestion, ChatRoleEnum.USER, nextChatIndex,
                    request.getQuestionUid(), intention, request.getTag());
        }
        insertMessage(request.getSessionId(), request.getUserId(), "", ChatRoleEnum.ASSISTANT, nextChatIndex, request.getAnswerUid(),
                intention, request.getTag());

    }

    private void insertMessage(String sessionUid, Long userId, String content, ChatRoleEnum chatRoleEnum, int chatIndex, String msgUid,
                               ChatIntentionEnum intention, String tag) {
        ChatMessageDO queryMessageDO = new ChatMessageDO();
        if (StringUtils.isNotEmpty(content)) {
            try {
                int maxBytes = 65500;
                byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
                if (contentBytes.length > maxBytes) {
                    content = new String(contentBytes, 0, maxBytes, StandardCharsets.UTF_8);
                }
            } catch (Throwable e) {
                LOGGER.warn("content转码失败，msgUid:{}", msgUid);
                content = content.substring(0, 65000 / 4);
            }
        }
        queryMessageDO.setContent(content);
        queryMessageDO.setRole(chatRoleEnum);
        queryMessageDO.setChatIndex(1);
        //lastMessageDO不为空说明是重新生成，需要对下标进行处理
        queryMessageDO.setChatIndex(chatIndex);
        queryMessageDO.setIntention(intention);
        queryMessageDO.setUserId(userId);
        queryMessageDO.setSessionUid(sessionUid);
        queryMessageDO.setUid(msgUid);
        queryMessageDO.setTag(tag);
        chatMessageService.insertSelective(queryMessageDO);
    }

    /**
     * 获取请求对话模型参数
     *
     * @param request  请求
     * @param maxRound 最大对话轮次
     * @return
     */
    private ChatRequestServerModel getChatRequestModel(ChatRequestBean request, int maxRound) {
        boolean ragFlag = false;
        String originalQuestion = request.getQuestion();
        //转换问题
        request.setQuestion(transQuestion(request));
        if (request.isRetry()) {
            //如果是重新生成答案，需要先删除之前的答案，并重新身成msgUid
            chatMessageService.deleteMessage(request.getAnswerUid());
            request.setAnswerUid(UUID.randomUUID().toString());
        } else {
            request.setQuestionUid(UUID.randomUUID().toString());
            request.setAnswerUid(UUID.randomUUID().toString());
        }
        ChatIntentionEnum intention = EnumUtil.fromString(ChatIntentionEnum.class, request.getIntention(), ChatIntentionEnum.CHAT);
        // 如果是异常信息分析场景将对话意图改成普通对话
        if (intention == ChatIntentionEnum.ERROR_ANALYZE) {
            intention = ChatIntentionEnum.CHAT;
        }
        String sessionUid = StringUtils.isBlank(request.getSessionId()) ? UUID.randomUUID().toString() : request.getSessionId();
        request.setSessionId(sessionUid);
        //如果不是单测就加入之前的多轮会话
        List<ChatMessageModel> chatMessages = new ArrayList<>(7);
        //集成测试多轮
        if (ChatIntentionEnum.CODE_GENERATE_INTERFACE_TEST.name().equals(request.getIntention())) {
            // 接口测试历史会话数据选择第1轮QA+最后N轮会话数据
            chatMessages = chatMessageService.listChatMessages(sessionUid, 2, maxRound);
        }
        //单测多轮
        else if (ChatIntentionEnum.CODE_GENERATE_TEST.name().equals(request.getIntention())) {
            LOGGER.info("[单测生成] 进入多轮会话能力，maxRound is {}, sessionUid is {}", maxRound, sessionUid);
            // 单测多轮对话：历史会话数据选择第1轮QA+最后N轮会话数据
            chatMessages = chatMessageService.listChatMessages(sessionUid, 2, maxRound);
            LOGGER.info("[单测生成] 进入多轮会话能力, chatMessages list size is {}", chatMessages.size());
        }
        // （对话、解释代码、优化代码、tag、agent、knowledge）
        else if (!KnowledgeTypeEnum.CODEBASE_LOCAL.name().equals(request.getKnowledgeType())) {
            String systemPrompt = drmConfig.getTalkSystemInfo();
            if (OptionLanguageTypeEnum.ENGLISH.getType().equals(request.getOptionLanguage())) {
                systemPrompt = systemPrompt.replace("中文", "英文");
            }
            //添加rag信息
            if (!CommonUtils.isKnowledge(request.getKnowledgeType()) && !CommonUtils.isAgent(request.getAgentType())
                    && isInNeedIntention(intention) && StringUtils.isBlank(request.getTag())) {
                chatMessages = chatMessageService.listChatMessagesWithIntention(sessionUid, 100, intention);
                chatMessages.add(0, new ChatMessageModel(ChatRoleEnum.SYSTEM.getAlgRequestName(), systemPrompt));
                ragForChatMessages(request, chatMessages);
                ragFlag = true;
            } else {
                chatMessages = chatMessageService.listChatMessagesWithIntention(sessionUid, maxRound, intention);
                chatMessages.add(0, new ChatMessageModel(ChatRoleEnum.SYSTEM.getAlgRequestName(), systemPrompt));
            }
        }
        // 本地仓库问答
        else if (null != request.getLocalCoreRagBO() && KnowledgeTypeEnum.CODEBASE_LOCAL.name().equals(request.getKnowledgeType())) {
            chatMessages = chatMessageService.listChatMessages(sessionUid, maxRound);
            chatMessages.add(0, new ChatMessageModel(ChatRoleEnum.SYSTEM.getAlgRequestName(), drmConfig.getLocalRepoChatSystemPrompt()));
            String referenceRagListStr = request.getLocalCoreRagBO().getReferenceRagListStr();
            referenceRagListStr = StringUtils.isBlank(referenceRagListStr) ? "搜索到的代码如下:\n "
                    : "搜索到的代码如下:\n" + referenceRagListStr;
            String selectedListStr = request.getLocalCoreRagBO().getSelectedListStr();
            selectedListStr = StringUtils.isBlank(selectedListStr) ? "用户圈选的代码如下:\n " : "用户圈选的代码如下:\n" + selectedListStr;
            chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), referenceRagListStr));
            chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), selectedListStr));
        }
        if (!ragFlag) {
            // 如果进行了rag，则问题已在rag中添加了，这里就不再添加了
            chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), request.getQuestion()));
        }
        //构建请求对象
        ChatRequestServerModel serviceRequest = buildChatRequestServerModel(request, chatMessages, sessionUid, intention);
        //先将空答案写入数据库
        saveDataToDatabase(request, intention, originalQuestion);
        return serviceRequest;
    }

    /**
     * 是否是所需要的意图（对话、代码解释）
     *
     * @param chatIntention
     * @return
     */
    private boolean isInNeedIntention(ChatIntentionEnum chatIntention) {
        return ChatIntentionEnum.CHAT == chatIntention;
    }

    /**
     * 为普通对话组装rag信息
     *
     * @param request
     * @param chatMessages
     */
    private void ragForChatMessages(ChatRequestBean request, List<ChatMessageModel> chatMessages) {
        try {
            if (CollectionUtils.isEmpty(request.getReferenceList())) {
                chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), request.getQuestion()));
                return;
            }
            // 将引用文件组装成rag信息
            StringBuilder referenceRagBuilder = new StringBuilder();
            List<CodeReferenceRequestBean> referenceList = request.getReferenceList();
            for (int i = 0; i < referenceList.size(); i++) {
                referenceRagBuilder.append(referenceList.get(i).getContent()).append(AppConstants.ENTER_SYMBOL);
            }
            if (StringUtils.isNotBlank(referenceRagBuilder.toString())) {
                String content = referenceRagBuilder.append(AppConstants.ENTER_SYMBOL).append(
                        request.getQuestion()).toString();
                chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), content));
            } else {
                chatMessages.add(new ChatMessageModel(ChatRoleEnum.USER.getAlgRequestName(), request.getQuestion()));
            }
        } catch (Throwable e) {
            LOGGER.warn("普通对话组装rag信息异常，request:{}", JSON.toJSONString(request), e);
        }
    }

    /**
     * 是否是询问当前打开文件相关问题
     *
     * @param question
     * @return
     */
    private boolean isAskAboutCurrentFile(String question) {
        return SELECTED_KEYWORDS.stream().anyMatch(question::contains);
    }

    /**
     * 构建服务端请求模型
     *
     * @param request
     * @param chatMessages
     * @param sessionUid
     * @return
     */
    private ChatRequestServerModel buildChatRequestServerModel(ChatRequestBean request, List<ChatMessageModel> chatMessages,
                                                               String sessionUid, ChatIntentionEnum intention) {
        AlgRequest<List<ChatMessageModel>> gptAlgRequest = new AlgRequest<>(chatMessages, drmConfig.getCodeGptModelConfig());
        ChatRequestServerModel serviceRequest = new ChatRequestServerModel(sessionUid, request.getUserId(), gptAlgRequest);
        serviceRequest.setLanguage(LanguageEnum.getLanguageEnum(request.getLanguage()));
        serviceRequest.setUserUid(request.getUserUid());
        serviceRequest.setProductType(EnumUtil.fromString(ProductTypeEnum.class, request.getProductType(), ProductTypeEnum.UNKNOWN));
        serviceRequest.setQuestionUid(request.getQuestionUid());
        serviceRequest.setAnswerUid(request.getAnswerUid());
        serviceRequest.setChatIntention(intention);
        serviceRequest.setChatIntentionContent(request.getChatIntentionContent());
        serviceRequest.setFileContent(request.getFileContent());
        serviceRequest.setTag(request.getTag());
        serviceRequest.setAgentType(request.getAgentType());
        serviceRequest.setRepoUrl(request.getRepo());
        serviceRequest.setCodeAnalyzeResult(request.getCodeAnalyzeResult());
        serviceRequest.setExtended(new HashMap<>(4));
        serviceRequest.setKnowledgeType(request.getKnowledgeType());
        serviceRequest.setBranch(request.getBranch());
        serviceRequest.setEnterType(request.getEnterType());
        serviceRequest.setOptionLanguage(request.getOptionLanguage());
        serviceRequest.setReferences(request.getReferenceList());
        serviceRequest.setContext(request.getRecentFilesInfo());
        serviceRequest.setAgentRequestBO(new AgentRequestBO(request.getAgentRag(), request.getAgentId(), request.getPluginCommand()));
        serviceRequest.setModelName(request.getModelName());
        serviceRequest.setModelType(request.getModelType());
        if (null != request.getLocalCoreRagBO()) {
            serviceRequest.setLocalRepoFileList(request.getLocalCoreRagBO().getFileList());
        }
        return serviceRequest;
    }

    /**
     * 转换提问，必须是同时满足单测、java语言、提问匹配才进行转换
     *
     * @param request
     * @return
     */
    private String transQuestion(ChatRequestBean request) {
        String requestIntention = request.getIntention();
        String language = request.getLanguage();
        String question = request.getQuestion();
        String tag = request.getTag();
        String sessionId = request.getSessionId();
        String head = CodeUtils.getCodeByType(question, CodeUtils.TYPE_HEAD, requestIntention);
        head = StringUtils.isBlank(head) ? question : head;
        String questionCode = CodeUtils.getCodeByType(question, CodeUtils.TYPE_CODE, requestIntention);
        if (ChatIntentionEnum.CODE_GENERATE_TEST.name().equals(requestIntention) || ChatIntentionEnum.CODE_GENERATE_INTERFACE_TEST.name()
                .equals(requestIntention)) {
            LOGGER.info("测试原始question：{}", getSimpleLogQuestion(question));
        } else {
            LOGGER.info("原始question：{}", question);
        }
        //问题为空直接返回
        if (StringUtils.isBlank(question)) {
            return question;
        }
        //解释代码
        if (ChatIntentionEnum.EXPLAIN_CODE.name().equals(requestIntention)) {
            // 当提问为解释代码，解释下代码意图时，先转换为更简单的解释 如：解释代码意图 --》更简单的解释
            if (StringUtils.isBlank(questionCode)) {
                questionCode = getCodeFromReferenceList(request);
            }
            question = getExplainQuestion(head, question, tag, questionCode);
        }
        //生成单测 为java、不是普通对话的情况才修改
        else if (ChatIntentionEnum.CODE_GENERATE_TEST.name().equals(requestIntention) && LanguageEnum.JAVA.name().equalsIgnoreCase(
                language)) {
            if (StringUtils.isBlank(questionCode)) {
                questionCode = getCodeFromReferenceList(request);
            }
            question = getTestQuestion(question, head, tag, questionCode);
        }
        //  简化代码
        else if (ChatIntentionEnum.CODE_SIMPLIFY.name().equals(requestIntention)) {
            // 简化或优化代码问题是直接将代码传过去
            question = questionCode;
        }
        // 优化代码(走普通对话模式)
        else if (ChatIntentionEnum.CODE_SUGGESTION_V2.name().equals(requestIntention)) {
            if (StringUtils.isBlank(questionCode)) {
                questionCode = getCodeFromReferenceList(request);
            }
            question = transCode(PromptConstants.CODE_SUGGESTION_V2_PROMPT, questionCode);
        }
        // 异常分析
        else if (ChatIntentionEnum.ERROR_ANALYZE.name().equals(requestIntention)) {
            question = getErrorAnalyzeQuestion(request, head, question, requestIntention);
        }
        // tag不为空即为二次引导 需要拼代码
        if (StringUtils.isNotBlank(tag)) {
            question = transQuestionForTag(request, sessionId, requestIntention, question);
        }
        if (ChatIntentionEnum.CODE_GENERATE_TEST.name().equals(requestIntention) || ChatIntentionEnum.CODE_GENERATE_INTERFACE_TEST.name()
                .equals(requestIntention)) {
            LOGGER.info("测试转换后的question：{}", getSimpleLogQuestion(question));
        } else {
            LOGGER.info("转换后的question：{}", question);
        }
        return question;
    }

    /**
     * 获取简化日志问题的长度
     *
     * @param question
     * @return
     */
    private String getSimpleLogQuestion(String question) {
        if (question == null) {return StringUtils.EMPTY;}
        return question.length() > 60 ? question.substring(0, 59) + "..." : question;
    }


    /**
     * 从referenceList中获取代码
     *
     * @param request
     * @return
     */
    private String getCodeFromReferenceList(ChatRequestBean request) {
        if (CollectionUtils.isNotEmpty(request.getReferenceList())) {
            CodegenChatRequestModel codegenChatRequestModel = JSONObject.parseObject(drmConfig.getTalkStreamBaseCheckConfig(),
                    CodegenChatRequestModel.class);
            int systemPromptTokenNum = tokenizedService.getTokenNum(drmConfig.getTalkSystemInfo());
            int questionTokenNum = tokenizedService.getTokenNum(request.getQuestion());
            int referTokenNum = tokenizedService.getTokenNum(JSON.toJSONString(request.getReferenceList()));
            // 多预留100 避免调模型超长
            if (codegenChatRequestModel.getMaxTokens() - systemPromptTokenNum - questionTokenNum - referTokenNum - 100 < 0) {
                // 基础校验 如果长度超过限制，直接抛异常让controller捕获
                throw new BizException(ResponseEnum.REFERENCE_TOO_LONG);
            }
        }
        String res = Optional.ofNullable(request.getReferenceList())
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .map(CodeReferenceRequestBean::getContent)
                        .collect(Collectors.joining(AppConstants.ENTER_SYMBOL + AppConstants.ENTER_SYMBOL)))
                .filter(StringUtils::isNotBlank)
                .orElse(StringUtils.EMPTY);
        if (StringUtils.isNotEmpty(res)) {
            return AppConstants.CODE_MARK + AppConstants.ENTER_SYMBOL + res + AppConstants.ENTER_SYMBOL + AppConstants.CODE_MARK;
        }
        return StringUtils.EMPTY;
    }

    /**
     * 二次引导拼接代码
     *
     * @param request
     * @param sessionId
     * @param requestIntention
     * @param question
     * @return
     */
    private String transQuestionForTag(ChatRequestBean request, String sessionId, String requestIntention, String question) {
        String code = getCodeBySessionId(sessionId, requestIntention);
        //这里生成单测的tag有两种情况，1、优化代码普通问答进来的，没有codeAnalyzeResult，但是能查到sessionId的代码
        //2、集成测试进来的，有codeAnalyzeResult，但查不到sessionId的代码
        //由于接口测试的引入，sessionId变化导致拿不到提问的代码，故生成单测且有codeAnalyzeResult的情况，代码直接从里面拿
        if (StringUtils.isBlank(code) && ChatIntentionEnum.CODE_GENERATE_TEST.name().equals(requestIntention) && StringUtils.isNotBlank(
                request.getCodeAnalyzeResult())) {
            try {
                StringBuilder stringBuilder = new StringBuilder(AppConstants.CODE_MARK).append(AppConstants.ENTER_SYMBOL);
                code = JSONObject.parseObject(request.getCodeAnalyzeResult()).getJSONObject("interface_prompt_data").getJSONObject(
                        "method_info").getString("code_body");
                code = stringBuilder.append(code).append(AppConstants.ENTER_SYMBOL).append(AppConstants.CODE_MARK).append(
                        AppConstants.ENTER_SYMBOL).toString();
            } catch (Throwable e) {
                LOGGER.warn("生成单测解析codeAnalyzeResult：", e);
                code = StringUtils.EMPTY;
            }
        }
        // 只有当 code 非空时才执行以下逻辑
        if (StringUtils.isNotBlank(code)) {
            // 如果 question 为空或空白，直接使用 code，否则将其追加到 question
            question = StringUtils.isBlank(question) ? code : transCode(question, code);
        }
        return question;
    }

    /**
     * 转换异常分析问题
     *
     * @param request
     * @param head
     * @param question
     * @param requestIntention
     * @return
     */
    private String getErrorAnalyzeQuestion(ChatRequestBean request, String head, String question, String requestIntention) {
        if (ProductTypeEnum.IDEA.name().equals(request.getProductType()) || ProductTypeEnum.CLOUD_IDE_IDEA.name().equals(
                request.getProductType())) {
            String code = head + CodeUtils.getCodeByType(question, CodeUtils.TYPE_CODE, requestIntention);
            question = transCode(ERROR_MESSAGE_PROMPT[1], code);
        } else {
            question = transCode(ERROR_MESSAGE_PROMPT[0], question);
        }
        return question;
    }

    /**
     * 转换生成单测问题
     *
     * @param question
     * @param head
     * @param tag
     * @param questionCode
     * @return
     */
    private String getTestQuestion(String question, String head, String tag, String questionCode) {
        // 多轮对话后面的轮次不要修改
        if (StringUtils.contains(question, "为以下代码") || StringUtils.contains(question, "生成单测")) {
            // 如果提问包含junit5，则使用junit5
            // 如果提问包含junit4，则使用junit4
            // 如果tag不为空，只修改提问的prompt，代码会在后续拼
            if (head.contains(CODE_GENERATE_TEST_TYPE_LIST[1])) {
                question = StringUtils.isNotBlank(tag) ? CODE_GENERATE_TEST_TYPE_LIST[3] : transCode(CODE_GENERATE_TEST_TYPE_LIST[3],
                        questionCode);
            } else {
                question = StringUtils.isNotBlank(tag) ? CODE_GENERATE_TEST_TYPE_LIST[2] : transCode(CODE_GENERATE_TEST_TYPE_LIST[2],
                        questionCode);
            }
        }
        return question;
    }

    /**
     * 转换解释代码问题
     *
     * @param head
     * @param question
     * @param tag
     * @param questionCode
     * @return
     */
    private String getExplainQuestion(String head, String question, String tag, String questionCode) {
        for (int i = 0; i < 2; i++) {
            if (head.startsWith(EXPLAIN_LANGUAGE_TYPE_LIST[i])) {
                question = EXPLAIN_LANGUAGE_TYPE_LIST[3];
                break;
            }
        }
        // 更简单的
        if (question.startsWith(EXPLAIN_MAP_LIST[0])) {
            question = PromptConstants.EASY_EXPLAIN_CODE_PROMPT;
        }
        //更详细
        else if (question.startsWith(EXPLAIN_MAP_LIST[1])) {
            question = PromptConstants.DETAILS_EXPLAIN_CODE_PROMPT;
        }
        question = StringUtils.isBlank(tag) ? transCode(question, questionCode) : question;
        return question;
    }

    /**
     * 获取代码
     *
     * @param sessionId
     * @return
     */
    private String getCodeBySessionId(String sessionId, String intention) {
        if (StringUtils.isBlank(sessionId)) {
            return StringUtils.EMPTY;
        }
        PageHelper.startPage(1, 1);
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andSessionUidEqualTo(sessionId).andTagIsNull().andRoleEqualTo(ChatRoleEnum.USER);
        chatMessageDOExample.setOrderByClause("id desc");
        List<ChatMessageDO> chatMessageDOS = chatMessageDOMapper.selectByExampleWithBLOBs(chatMessageDOExample);
        // 使用Optional来简化空检查
        if (CollectionUtils.isEmpty(chatMessageDOS)) {
            return StringUtils.EMPTY;
        }
        return CodeUtils.getCodeByType(chatMessageDOS.get(0).getContent(), CodeUtils.TYPE_CODE, intention);
    }

    /**
     * 组装新的prompt question
     *
     * @param prompt
     * @param code
     * @return
     */
    private String transCode(String prompt, String code) {
        //这里是为了解决优化代码prompt导致无法准确获取用户提问代码问题
        String[] split = code.split(LAST_QUESTION_IS_OPTIMIZE_CODE_MARK);
        code = split.length > 1 ? split[1] : split[0];
        if (StringUtils.isNotBlank(code)) {
            if (code.startsWith(":\n") || code.startsWith("：\n")) {
                return new StringBuilder(prompt).append(code).toString();
            }
            return new StringBuilder(prompt).append("：").append(AppConstants.ENTER_SYMBOL).append(code).toString();
        }
        return prompt;
    }

    /**
     * 添加功能权限
     *
     * @param userDO
     * @param pluginConfigDataResponse
     */
    private void addFunctionPermissions(UserDO userDO, PluginConfigDataResponse pluginConfigDataResponse) {
        // 功能权限列表信息
        List<String> functionPermissions = new ArrayList<>(3);
        // agent扩展信息
        Map<String, Object> agentExtendInfo = new HashMap<>(4);

        // 全量权限
        functionPermissions.add(FunctionPermissionsEnum.PICTURE_TO_CODE.getPermissionName());
        functionPermissions.add(FunctionPermissionsEnum.CODEBASE.getPermissionName());
        functionPermissions.add(FunctionPermissionsEnum.ACTION_TO_CODE.getPermissionName());
        functionPermissions.add(FunctionPermissionsEnum.TERMINAL.getPermissionName());
        functionPermissions.add(FunctionPermissionsEnum.AI_PARTNER.getPermissionName());
        // 查询drm中agent扩展信息
        Map<String, Object> agentListJsonObject = JSON.parseObject(drmConfig.getAgentList(), new TypeReference<>() {});
        agentExtendInfo.put(FunctionPermissionsEnum.TERMINAL.getPermissionName(),
                agentListJsonObject.get(FunctionPermissionsEnum.fromName(FunctionPermissionsEnum.TERMINAL.getPermissionName()).name()));
        pluginConfigDataResponse.setFunctionPermissions(functionPermissions);
        pluginConfigDataResponse.setAgentExtInfo(agentExtendInfo);

        // 分流权限
        if (null != userDO) {
            String empId = CommonUtils.formatSassUserId(userDO.getSaasUserId());

            // 对话是否走rag分流
            functionPermissionFromAbTest(LOCAL_AGENT_CHAT_RAG_PERMISSIONS, empId, FunctionPermissionsStatus.CLOSE.name(),
                    functionPermissions, FunctionPermissionsEnum.CHAT_RAG.getPermissionName(), agentExtendInfo, agentListJsonObject);
            //workFlow分流
            functionPermissionFromAbTest(WORK_FLOW_API_PERMISSIONS, empId, FunctionPermissionsStatus.CLOSE.name(),
                    functionPermissions, FunctionPermissionsEnum.WORK_FLOW_API.getPermissionName(), null, null);
            // 集成测试配置AB分流
            String type = abTestService.abTest(AbTestConstants.INTENTION_TYPE_AB_KEY, empId, empId, "UNIT_TEST");
            pluginConfigDataResponse.setIntentionType(type);

            // 动态时间开关分流
            String enableDebounceTimeFlag = abTestService.abTest(AbTestConstants.DEBOUNCE_TIME_AB_KEY, empId, empId, "true");
            pluginConfigDataResponse.setEnableDebounceTimeFlag(Boolean.parseBoolean(enableDebounceTimeFlag));

            // 线性、树性模型AB分流
            String linearModelFlag = abTestService.abTest(AbTestConstants.LINEAR_MODEL_AB_KEY, empId, empId, "true");
            pluginConfigDataResponse.setLinearModelFlag(Boolean.parseBoolean(linearModelFlag));

            pluginConfigDataResponse.setFunctionPermissions(functionPermissions);
            pluginConfigDataResponse.setAgentExtInfo(agentExtendInfo);
        }
    }

    /**
     * 添加deepSearch白名单权限
     * @param userDO
     * @param pluginConfigDataResponse
     */
    private void addDeepSearchPermissions(UserDO userDO, PluginConfigDataResponse pluginConfigDataResponse){
        try{
            if (null != userDO) {
                String empId = CommonUtils.formatSassUserId(userDO.getSaasUserId());
                //codeBase分流
                pluginConfigDataResponse.setEnableRepoDeepSearch(deepSearchPermissionFromAbTest(DEEP_SEARCH_CODE_BASE_PERMISSION, empId, FunctionPermissionsStatus.CLOSE.name()));
                //解释代码分流
                pluginConfigDataResponse.setEnableExplainDeepSearch(deepSearchPermissionFromAbTest(DEEP_SEARCH_EXPLAIN_CODE_PERMISSION, empId, FunctionPermissionsStatus.CLOSE.name()));
                //APar分流
                pluginConfigDataResponse.setEnableAPDeepSearch(deepSearchPermissionFromAbTest(DEEP_SEARCH_AI_PARTNER_AGENT_PERMISSION, empId, FunctionPermissionsStatus.CLOSE.name()));
            }
        }catch (Throwable e){
            LOGGER.error("addDeepSearchPermissions error", e);
        }
    }


    /**
     * 功能权限分流
     *
     * @param sceneKey            分流key
     * @param userId              用户id
     * @param defaultValue        默认值
     * @param functionPermissions 功能权限列表信息
     * @param permissionName      功能权限标志
     * @param agentExtendInfo     agent扩展信息
     * @param agentListJsonObject drm中agent扩展信息
     */
    private void functionPermissionFromAbTest(String sceneKey, String userId, String defaultValue, List<String> functionPermissions,
                                              String permissionName, Map<String, Object> agentExtendInfo,
                                              Map<String, Object> agentListJsonObject) {
        // 根据key进行分流实验
        String permissionStatus = abTestService.abTest(sceneKey, userId, userId, defaultValue);

        // 如果是OPEN状态则将当前功能添加到集合中
        if (FunctionPermissionsStatus.OPEN.name().equals(permissionStatus)) {
            functionPermissions.add(permissionName);
            // 如果功能权限属于agent范围内则获取到扩展信息
            if (isPermissionNeedExtendInfo(permissionName) && null != agentListJsonObject.get(
                    FunctionPermissionsEnum.fromName(permissionName).name())) {
                agentExtendInfo.put(permissionName, agentListJsonObject.get(FunctionPermissionsEnum.fromName(permissionName).name()));
            }
        }
    }

    /**
     * DeepSearch权限分流
     *
     * @param sceneKey            分流key
     * @param userId              用户id
     * @param defaultValue        默认值
     */
    private Boolean deepSearchPermissionFromAbTest(String sceneKey, String userId, String defaultValue) {
        // 根据key进行分流实验
        String permissionStatus = abTestService.abTest(sceneKey, userId, userId, defaultValue);
        return FunctionPermissionsStatus.OPEN.name().equals(permissionStatus);
    }


    /**
     * 功能权限是否需要扩展信息
     *
     * @param permissionName
     * @return
     */
    private boolean isPermissionNeedExtendInfo(String permissionName) {
        if (StringUtils.isEmpty(permissionName)) {
            return false;
        }
        if (permissionName.startsWith(AGENT_PREFIX) || permissionName.startsWith(KNOWLEDGE_PREFIX)) {
            return true;
        }
        return false;
    }

    /**
     * 判断token是否超过最大限制
     *
     * @param promptStr
     * @return
     */
    private void isMaxToken(StringBuilder promptStr) {
        // 判断token是否超过最大限制32K
        CodeGptModelConfig config = JSONObject.parseObject(drmConfig.getChatModelMayaConfig()).getJSONObject(
                IntentionEnum.INTENTION_RECOGNITION.name()).toJavaObject(CodeGptModelConfig.class);
        Integer promptMaxToken = null != config.getPromptMaxToken() ? config.getPromptMaxToken() : 32000;
        int tokenNum = tokenizedService.getTokenNum(promptStr.toString());
        if (tokenNum > promptMaxToken) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "failed");
        }
    }

    /**
     * 意图规则匹配
     *
     * @param question
     * @return
     */
    private String intentionMatching(String question) {
        String intention = StringUtils.EMPTY;
        // 读取drm配置拿到规则集合json串
        JSONObject jsonObject = JSON.parseObject(drmConfig.getIntentionRecognitionMatching());
        // 解析获取对话、代码改写规则集合
        List<String> codeUpdateList = JSON.parseArray(jsonObject.getJSONArray("CODEUPDATE").toJSONString(), String.class);
        List<String> codeChatList = JSON.parseArray(jsonObject.getJSONArray("CODECHAT").toJSONString(), String.class);
        // 多意图走对话
        if (countMatchingKeywords(question, codeChatList) + countMatchingKeywords(question, codeUpdateList) >= 2) {
            intention = ChatIntentionEnum.CHAT.name();
            LOGGER.info("多意图走对话匹配规则：{}", intention);
        } else if (countMatchingKeywords(question, codeChatList) > 0) {
            intention = ChatIntentionEnum.CHAT.name();
            LOGGER.info("走对话匹配规则：{}", intention);
        } else if (countMatchingKeywords(question, codeUpdateList) > 0) {
            intention = IntentionEnum.CODE_GENERATE.name();
            LOGGER.info("走代码修改匹配规则：{}", intention);
        }
        return intention;
    }

    /**
     * 是否包含意图识别规则关键字
     *
     * @param query
     * @param keyWords
     * @return
     */
    private int countMatchingKeywords(String query, List<String> keyWords) {
        return (int) keyWords.stream().filter(query::contains).count();
    }

    /**
     * 获取模型配置
     *
     * @param intentionEnum
     * @return
     */
    private CodeGptModelConfig getCodeGptModelConfig(IntentionEnum intentionEnum) {
        String intentionKey = intentionEnum.name();
        //query改写和代码总结用本地仓库问答的模型
        if (IntentionEnum.QUERY_CHANGE == intentionEnum || IntentionEnum.CODE_SUMMARY == intentionEnum) {
            intentionKey = IntentionEnum.LOCAL_REPO.name();
        }
        CodeGptModelConfig config = null;
        try {
            config = JSONObject.parseObject(drmConfig.getChatModelMayaConfig()).getJSONObject(intentionKey).toJavaObject(
                    CodeGptModelConfig.class);
        } catch (Throwable e) {
            LOGGER.warn("CodeFuseServiceImpl.getCodeGptModelConfig error, intentionKey: {}, e: {}", intentionKey, e);
        }
        return config;
    }

    /**
     * 获取服务请求参数
     *
     * @param request
     * @param gptAlgRequest
     * @return
     */
    private MayaStreamRequestServerModel buildMayaStreamChatRequestServerModel(FunctionOnStreamRequestBean request,
                                                                               AlgRequest<List<ChatMessageModel>> gptAlgRequest) {
        MayaStreamRequestServerModel serviceRequest = new MayaStreamRequestServerModel(request.getQuestionUid(), request.getUserId(),
                gptAlgRequest);
        serviceRequest.setProductType(EnumUtil.fromString(ProductTypeEnum.class, request.getProductType(), ProductTypeEnum.UNKNOWN));
        serviceRequest.setQuestionUid(request.getQuestionUid());
        serviceRequest.setAnswerUid(request.getAnswerUid());
        serviceRequest.setSessionId(request.getSessionId());
        serviceRequest.setTraceId(request.getTraceId());
        serviceRequest.setUserUid(request.getUserUid());
        serviceRequest.setExtended(new HashMap<String, Object>(4));
        serviceRequest.setIntention(request.getIntention());
        serviceRequest.setQuestionCode(request.getQuestionCode());
        serviceRequest.setLanguage(LanguageEnum.getLanguageEnum(request.getLanguage()));
        serviceRequest.setModelName(request.getModelName());
        serviceRequest.setModelType(request.getModelType());
        serviceRequest.setFileUrl(request.getFileUrl());
        return serviceRequest;
    }

    /**
     * 根据产品类型和token查询用户信息
     *
     * @param productTypeEnum 产品类型
     * @param userToken       用户token
     * @return UserDO
     */
    private UserDO queryUserByProductAndToken(ProductTypeEnum productTypeEnum, String userToken) {
        UserDO userDO = userService.queryUserToDO(userToken, productTypeEnum);

        if (userDO == null) {
            LOGGER.error("查询用户信息为空,产品名: {} , userToken: {}", productTypeEnum, userToken);
            throw new InvalidReqException(ResponseEnum.REQ_INVALID_USERINFO);
        }
        return userDO;
    }

    /**
     * 插件升级分流
     *
     * @param request
     * @param pluginConfigDataResponse
     * @param userDO
     */
    private void pluginUpgradeConfig(QueryPluginConfigRequestBean request, PluginConfigDataResponse pluginConfigDataResponse,
                                     UserDO userDO) {
        // 判断drm中是否配置了强制升级版本号
        if (StringUtils.isNotBlank(bizSwitch.getForcedVersionUpgrade())) {
            Map<String, String> forcedUpgradeMap = JSON.parseObject(bizSwitch.getForcedVersionUpgrade(),
                    new TypeReference<Map<String, String>>() {});
            // 如果drm配置需要强制升级的版本号中存在当前用户正在使用的版本号，则需要强制升级
            if (forcedUpgradeMap.containsKey(request.getPluginVersion())) {
                pluginConfigDataResponse.setPluginUpdateModel(
                        new PluginUpdateModel(PluginUpdateStatusEnum.FORCE.name(), forcedUpgradeMap.get(request.getPluginVersion())));
            }
        }
        // 根据产品类型判断是否走单独升级分流
        Map<String, String> productTypeUpgradeMap = JSON.parseObject(drmConfig.getUpgradeByProductType(),
                new TypeReference<Map<String, String>>() {});
        // 配置中存在则走单独的产品类型升级分流逻辑s
        if (null != productTypeUpgradeMap && productTypeUpgradeMap.containsKey(request.getProductType())) {
            String ideUpgradesFlag = abTestService.abTest(productTypeUpgradeMap.get(request.getProductType()),
                    CommonUtils.formatSassUserId(userDO.getSaasUserId()), CommonUtils.formatSassUserId(userDO.getSaasUserId()),
                    PluginUpdateStatusEnum.NONE.name());
            pluginConfigDataResponse.setPluginUpdateModel(new PluginUpdateModel(ideUpgradesFlag, null));
        } else {
            // 走普通自动升级分流逻辑
            String upgradesFlag = abTestService.abTest(AbTestConstants.PLUGIN_UPGRADE_KEY,
                    CommonUtils.formatSassUserId(userDO.getSaasUserId()), CommonUtils.formatSassUserId(userDO.getSaasUserId()),
                    PluginUpdateStatusEnum.NONE.name());
            pluginConfigDataResponse.setPluginUpdateModel(new PluginUpdateModel(upgradesFlag, null));
        }
    }

}
