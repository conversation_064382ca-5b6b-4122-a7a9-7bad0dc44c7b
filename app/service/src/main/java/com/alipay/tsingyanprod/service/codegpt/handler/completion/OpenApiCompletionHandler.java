/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.tsingyanprod.service.codegpt.handler.completion;

import com.alipay.tsingyanprod.model.model.TaskContextModel;
import com.alipay.tsingyanprod.model.request.openapi.OpenApiCompletionRequestBean;
import com.alipay.tsingyanprod.model.response.CompletionAlgResponse;
import com.alipay.tsingyanprod.service.codegpt.external.alg.service.CompletionService;

/**
 * <AUTHOR>
 * @version OpenApiCompletionHandler.java, v 0.1 2023年07月05日 11:05 xiaobin
 */
public class OpenApiCompletionHandler extends AbstractCompletionAlgHandler<OpenApiCompletionRequestBean, CompletionAlgResponse> {

    /**
     * 抽象handler构造函数
     *
     * @param server 算法服务
     */
    public OpenApiCompletionHandler(CompletionService server) {
        super(server);
    }

    @Override
    public CompletionAlgResponse codeCompletion(TaskContextModel<OpenApiCompletionRequestBean, CompletionAlgResponse> taskContextModel) {
        OpenApiCompletionRequestBean request = taskContextModel.getRequest();
        MODEL_LOGGER.info("开始执行OpenAPI代码补全处理，sessionId: {}, empId: {}, language: {}",
                request.getSessionId(), request.getEmpId(), request.getLanguage());

        CompletionAlgResponse response = completionAlgService.completionByOpenApi(request,
                taskContextModel.getRecord());
        taskContextModel.setResult(response);

        MODEL_LOGGER.info("OpenAPI代码补全处理完成，sessionId: {}, 生成结果数量: {}",
                request.getSessionId(), response != null && response.getGeneratedTextArr() != null ? response.getGeneratedTextArr().size() : 0);
        return null;
    }
}